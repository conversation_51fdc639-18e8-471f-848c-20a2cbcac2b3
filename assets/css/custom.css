[v-cloak] {
    display: none;
}
.btn-inactive {
    cursor: not-allowed !important;
    opacity: 0.4 !important;
    pointer-events: none !important;
}
.alert {
    max-width: 39em !important;
}
.fadeInDown {
    top: 5em !important;
}
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 24px;
    margin-right: 8px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked + .slider {
    background-color: #009D75;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
    -webkit-transform: translateX(34px);
    -ms-transform: translateX(34px);
    transform: translateX(34px);
}

/* Rounded sliders */
.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}
.live {
    background-color: red !important;
    flex-grow: 0;
    min-width: 60px;
}
.ended {
    background-color: black !important;
}
.blink {
    background: #fed451;
}
.blink {
    font-weight: 500;
    display: inline-block;
    padding: 0 2px;
    color: #222;
    vertical-align: middle;
}
.blink {
    animation: blink-animation 0.5s steps(5, start) infinite;
    -webkit-animation: blink-animation 0.5s steps(5, start) infinite;
}
@keyframes blink-animation {
    to {
        visibility: hidden;
    }
}
@-webkit-keyframes blink-animation {
    to {
        visibility: hidden;
    }
}
.team-form .btn {
    width: 22px;
}
.btn-odd-option .odd-selection {
    cursor: pointer;
}
.my-teams a {
    padding: 4px;
}
.odds-deactivated {
    color: #b71c1c !important;
}
.cancel {
    cursor: pointer;
}
.event .event-t {
    justify-content: center !important;
}
.my-teams-number {
    font-size: 17px;
}
.game {
    display: block;
    background: #000;
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: auto;
}
.game iframe {
    display: block;
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: auto;
    border: none;
    outline: none;
}
.game-action-btn a {
    width: 44%;
    padding: 10px;
    font-size: 1em;
}
.outcome-signs {
    cursor: pointer;
}
::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: lightgrey !important;
    opacity: 1; /* Firefox */
}
.material-icons{
    color: #212529 !important;
}
.other-markets-wrapper.other-markets .btn-odd-option .odd-selection {
    width: 56px;
    margin-bottom: 2px !important;
    min-width: 56px;
}
.event .event-t {
    justify-content: space-evenly !important;
}
select option {
    background: var(--color-secondary);
    color: #fff;
    border: none;
}
.team-jersey {
    height: 20px;
    width: 20px;
}
.outcome-header {
    font-weight: bold;
    font-size: 18px;
}
.share-bet-stake-and-wrap {
    position: inherit !important;
}
.paybill-borer {
    border: 1px solid white;
    color: white;
    padding: 15px;
    font-size: 17px;
    text-align: left;
    line-height: normal;
}
.list-ordered {
    list-style: auto !important;
}
.d-bonus {
    text-align: left !important;
}
.d-bonus ul{
    list-style: disc !important;
    line-height: 22px;
}
.d-bonus label {
    font-size: 17px;
    color: white;
}
#deposit input[type="text"],
#deposit input[type="number"] {
    color: #495057;
    background-color: transparent !important;
    background-clip: padding-box;
    border: 2px solid #f97c00;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    padding: 9px 15px;
    line-height: 10px;
    width: 100%;
    font-size: 22px;
}
.money-box-image-selected{
    border: 2px solid #f97c00;
}
#deposit .deposit-btn .active {
    background-color: #f97c00 !important;
}
.money-box-image {
    cursor: pointer;
}
.card-header-tabs li.nav-item {
    width: 33.3% !important;
}
.instant-games-balance h5 {
    color: white;
}
.league-wrapper {
    padding-bottom: 0 !important;
    min-height: auto !important;
}
.away-team {
    font-size: var(--font-medium);
    text-transform: none !important;
}
.home-team {
    font-size: var(--font-medium);
    text-transform: capitalize;
}
.score-time {
    padding: 0 3px;
    font-size: 12px;
    color: #FFF;
}
.score-time > span {
    padding: 0 3px;
}
.col-time-c > .score-time {
    font-size: 12px;
    color: #FFF;
}
.playing-teams > .name{
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
    letter-spacing: 0em;
    font-size: 1em;
    text-align: left;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    /* padding: 2px 0; */
    border: none;
    color: black !important;
}
.timer1 {
    color: #fff;
    background-color: #f00 !important;
    font-weight: 600;
    padding: 10px 15px;
    font-size: 13px;
    text-align: center;
}
.select-t {
    font-size: 14px !important;
}
.select-t::after {
    top: 3px !important;
}
.filter-by {
    color: white;
    font-size: 17px;
    padding-top: 14px;
    padding-left: 23px;
}
.jackpot-timer a.active {
    color: #009d75 !important;
    border-bottom: 2px solid #009d75;
}
.live.active {
    color: white !important;
}
.stake-input {
    cursor: pointer;
}
.home-team, .away-team {
    line-height: 18px !important;
}
.jackpot-tab #bet-slip a {
    display: flex !important;
    align-items: center;
    flex-direction: row;
    justify-content: center;
    color: #fff;
    font-size: 12px;
}
.jackpot-tab #bet-slip a img {
    max-width: 25px;
    margin: 0 2px;
    width: 25px;
}
.my-slip-teams {
    width: 35px;
    height: 35px;
}
.btn-primary {
    color: #fff;
    background-color: var(--color-active) !important;
    border-color: var(--color-active);
}
.tab-details .my-slip-teams {
    width: 35px;
    height: 35px;
}
.tab-details .my-slip-teams {
    font-size: 0.9em;
}
.tab-details .odds-container {
    background-color: inherit;
}
.shine {
    background: var(--bg-odd-button) linear-gradient(to right, var(--bg-odd-button) 0%, var(--border-color-odd-button) 20%, var(--bg-odd-button) 40%, var(--bg-odd-button) 100%) no-repeat;
    background-size: 800px 104px;
    display: inline-block;
    position: relative;
    -webkit-animation-duration: 1s;
    -webkit-animation-fill-mode: forwards;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-name: placeholderShimmer;
    -webkit-animation-timing-function: linear;
}
@-webkit-keyframes placeholderShimmer {
    0% {
        background-position: -468px 0;
    }

    100% {
        background-position: 468px 0;
    }
}
.shine .ods-title {
    color: transparent;
}
.shine .ods-given {
    color: transparent;
}
.shine .team-name-text {
    color: transparent;
}
.home-away.shine {
    border-radius: 8px;
}
.pb-16 {
    padding-bottom: 16em;
}
.pb-17 {
    padding-bottom: 17em;
}
.pb-19 {
    padding-bottom: 19em;
}
.pb-12 {
    padding-bottom: 12em !important;
}

.progressbarwidget .odds-progress {
    background-color: #fff3cd;
    border-color: #ffeeba;
    border-radius: 0.25rem;
}

.progressbarwidget .odds-progress .progress-bar {
    font-size: 11px;
}

.progressbarwidget .odds-bg {
    background-color: #d1ecf1 !important;
    color: #0c5460;
    height: 50px;
    border-radius: 0.25rem;
}

.progressbarwidget .alert-dark {
    border: none;
    padding: 0;
}

.progressbarwidget .alert-dismissible .close{
    padding: 0.80rem 0.35rem;
    font-size: 16px;
}

.progressbarwidget .alert {
    max-width: 100% !important;
    margin-bottom: 0 !important;
}
.bold {
    font-weight: 600;
}
