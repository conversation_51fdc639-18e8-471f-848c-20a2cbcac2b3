
   /*--------- OWL Style -------*/

#owl-demo .item{
	position: relative;
}
#owl-demo .item img{
	display: block;
	width: 100%;
	height: auto;
}
.owl-pagination{ display:none
}
#owl-demo .caption-wrap{
	position:absolute;
	width:100%;
	padding: 10px;
	bottom: 0;
	background: rgb(0,0,0);
	background: linear-gradient(180deg, rgba(0,0,0,0.18671218487394958) 60%, rgba(0,0,0,1) 100%);
	height: 100%;
}
#owl-demo .caption{
	position:absolute;
	width:100%;
	padding: 10px;
	bottom: 0;
	padding-bottom: 30px;
	}
#owl-demo .caption h5{
	background-color: #00A8A7;
	padding: 7px 10px;
	color: #fff;
	text-transform: uppercase;
	display: inline-block;
	font-weight: 200;
}
#owl-demo .caption h6{
	color: #fff;
	padding: 0;
	margin: 10px 0;
	width: 70%;
	font-weight: 200;
	line-height: 1.2;
}
#owl-demo .caption .date{
	font-size: 18px;
	color: #ADCB54;
	font-weight: 900;
}

.owl-carousel .owl-wrapper:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}
/* display none until init */
.owl-carousel{
	display: none;
	position: relative;
	width: 100%;
	-ms-touch-action: pan-y;
}
.owl-carousel .owl-wrapper{
	display: none;
	position: relative;
	-webkit-transform: translate3d(0px, 0px, 0px);
}
.owl-carousel .owl-wrapper-outer{
	overflow: hidden;
	position: relative;
	width: 100%;
}
.owl-carousel .owl-wrapper-outer.autoHeight{
	-webkit-transition: height 500ms ease-in-out;
	-moz-transition: height 500ms ease-in-out;
	-ms-transition: height 500ms ease-in-out;
	-o-transition: height 500ms ease-in-out;
	transition: height 500ms ease-in-out;
}
	
.owl-carousel .owl-item{
	float: left;
}
.owl-controls .owl-page,
.owl-controls .owl-buttons div{
	cursor: pointer;
}
.owl-controls {
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* mouse grab icon */
.grabbing { 
    cursor:url(grabbing.png) 8 8, move;
}

/* fix */
.owl-carousel  .owl-wrapper,
.owl-carousel  .owl-item{
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility:    hidden;
	-ms-backface-visibility:     hidden;
  -webkit-transform: translate3d(0,0,0);
  -moz-transform: translate3d(0,0,0);
  -ms-transform: translate3d(0,0,0);
}

