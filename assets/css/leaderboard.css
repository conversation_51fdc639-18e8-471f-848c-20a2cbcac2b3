@import url('https://fonts.googleapis.com/css2?family=Cabin+Condensed:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@300;400&display=swap');
/*---------- RESET ----------*/
html, body { height: 100%;}
:root {
    --bg-active: #fab135;
    --color-primary: #34495e;
    --color-secondary: #0a1f2f;
    --color-accent: #23313d;
    --bg-markets: #04011b;
    --color-active: #37b34a;
    --color-market-button: #ffffff;
    --bg-market-button: transparent;
    --bg-market-wrap-odd: #0a1f2f;
    --bg-market-wrap-even: #23313d;
    --bg-odd-button: #34495e;
    --border-color-odd-button: #34495e;
    --bg-odd-button-active: #fab135;
    --border-color-odd-button-active: #fab135;
    --color-odds-title: #fff;
    --color-odds: #fff;
    --color-odds-title-active: #000000;
    --color-odds-active: #000000;
    --bg-odds-container: #04011b;
    --bg-my-container: #34495e;
    --bg-betslip: #23313d;
    --color-betslip: #ffffff;
    --color-betslip-market: #98a9bc;
    --color-betslip-odd: #98a9bc;
    --bg-placebet-button: #37b34a;
}
body {background-color: #f2f4f6;
    line-height: 1;
    -webkit-text-size-adjust: none;
}
body, select, input {
    font-family: 'Cabin Condensed', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn,
em, font,img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label,
legend, table,caption, tbody, tfoot, thead, tr, th,td {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    vertical-align: baseline;
    background-color: transparent;
    text-decoration: none;
}

article, aside, footer, header, hgroup, nav, section, figure, figcaption {
    display: block;
    margin: 0;
}

* {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

input,
textarea {
    -webkit-border-radius: 0;
    border-radius: 0;
}

input[type="submit"],
input[type="button"] {
    -webkit-appearance: none;
}

ol,
ul {
    list-style: none;
}

blockquote,
q {
    quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
    content: "";
    content: none;
}

:focus {
    outline: 0;
}

::-moz-focus-inner {
    border: 0;
}

ins {
    text-decoration: none;
}

del {
    text-decoration: line-through;
}

*,
*:after,
*:before {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

* {
    box-sizing: border-box;
}

body {
    background-color: #f2f4f6;
    font-family: 'Cabin Condensed', sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 13px;
    height: auto;
    background-image: url('https://storage.googleapis.com/kironlite/images/mossbet-1681720269.jpg');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
}

body p {
    padding-bottom: 1em;
}

.clearfloat {
    zoom: 1;
}

.clearfloat:before,
.clearfloat:after {
    content: "";
    display: table;
}

.clearfloat:after {
    clear: both;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-transition-delay: 9999s;
    transition-delay: 9999s;
}

input {
    background-color: var(--color-accent) !important;
    color: #fff !important;
}

input:-webkit-autofill {
    background-color: var(--bg-markets) !important;
    color: #ffff;
}
input:autofill {
    background-color: var(--bg-markets) !important;
    color: #fff;
}

::placeholder {
    /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: #ffffff;
    opacity: 1; /* Firefox */
}

:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: #ffffff;
}

::-ms-input-placeholder {
    /* Microsoft Edge */
    color: #ffffff;
}

::-webkit-scrollbar {
    display: none;
    width: 0;  /* Remove scrollbar space */
    background: transparent;  /* Optional: just make scrollbar invisible */
}

.app-bet-time::-webkit-scrollbar {
    display: none;
}

.app-bet-time::-webkit-scrollbar-track {
    background: var(--bg-markets);
}
.app-bet-time::-webkit-scrollbar-thumb {
    background: var(--bg-markets);
}
.app-bet-time::-webkit-scrollbar-thumb:hover {
    background: var(--bg-markets);
}


a {outline: none;
    -webkit-transition: all 0.5s ease 0s;
    transition: all 0.5s ease 0s;
}

a:hover {
    text-decoration: none;
}

img {
    width: 100%;
    display: block;
}

.clearfloat {
    zoom: 1;
}

.clearfloat:before,
.clearfloat:after {
    content: "";
    display: table;
}

.clearfloat:after {
    clear: both;
}

/*--------- Main Style -------*/

#app-container {
    max-width: 540px;
    width: 100%;
    margin: 0 auto;
    padding: 0;
    background: #34495e;
    overflow-x: hidden;
    background-color: var(--bg-market-wrap-even);
}

#app {
    max-width: 540px;
    width: 100%;
    float: left;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    background: #34495e;
    background-color: var(--bg-market-wrap-even);
}

#main-content {
    max-width: 540px;
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--bg-market-wrap-odd);
}

#other-main-content {
    max-width: 540px;
    width: 100%;
    display: flex;
    flex-direction: column;
    /*padding-top: 4em;*/
}

.main-app-main-header {
    max-width: 540px;
    width: 100%;
    transition: top 0.2s ease-in-out;
}

#slider-banner {
    width: 100%;
    float: left;
}

.fixed-header {
    transition: top 0.2s ease-in-out;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    margin: 0 auto;
    z-index: 100;
}

.deposit {
    width: 100%;
    text-transform: uppercase;
    display: block;
    padding-top: 15px;
    padding-bottom: 20px;
    padding-left: 20px;
    padding-right: 20px;
}

.deposit span {
    width: 50%;
    font-size: 12px;
}

.deposit a {
    color: #fff;
    font-weight: 600;
    letter-spacing: 0.3;
    font-size: 8px;
}

.deposit a .paybill {
    color: #56a451;
}

.dropdown-arrow img {
    max-width: 10px;
    padding-left: 2px;
}

.logo-section {
    padding: 0.5em 0;
    width: 100%;
    max-width: 540px;
    background-color: var(--color-accent);
    background-color: var(--bg-market-wrap-odd);
    height: 2.5em;
    display: flex;
    align-items: center;
}

.logo-section .row {
    display: flex;
    align-items: center;
}

.logo-section a {
    display: block;
    text-align: center;
}

.logo-section img {
    display: inline-block;
}

.menu-icon {
    float: left;
    text-align: center;
    display: flex;
    justify-content: flex-start;
}

.menu-icon img {
    max-width: 40px;
    width: 40px;
}

.logo-icon {
    width: 100%;
    text-align: center !important;
}

.logo-icon img {
    max-width: 100px;
}

.login-nav {
    border-radius: 5px !important;
    margin-right: 5px;
}

.account-access {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    padding-top: 0 !important;
}

.account-access a {
    color: #ffff;
    text-transform: capitalize !important;
    font-size: 1.3em;
    width: 100%;
}

.account-access a:hover {
    color: var(--color-active);
}

.login-nav a {
    background-color: transparent;
    border: 2px solid var(--color-active) !important;
    padding: 7px 7px;
    border-radius: 5px !important;
}

.register-nav {
    display: flex;
    justify-content: center;
    flex-direction: row;
}

.register-nav a {
    background-color: var(--color-active);
    border: 2px solid var(--color-active) !important;
    padding: 7px 10px;
    border-radius: 5px !important;
    color: var(--color-active);
    text-align: center;
    display: inline-block;
}

.account-balance-wrap a {
    border: 2px solid var(--color-active) !important;
}

.league-account {
    display: flex;
    justify-content: center;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    color: #fff;
}

.league-account .account-balance {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-content: stretch;
}

.league-account a {
    color: #ffffff;
    font-size: 10px;
}

.nav-betslip {
    display: flex;
    text-align: center;
    justify-content: space-between;
}

.nav-betslip span {
    display: block;
}

.league-account img {
    width: 15px;
}

.league-account .account-balance span {
    width: 100%;
    display: block;
    text-align: center;
    padding: 2px 0;
}

.app-sports-icons {
    background-color: #212335;
    width: 100%;
    float: left;
    max-width: 540px;
    display: block;
    text-align: center;
}

.scrollmenu {
    white-space: nowrap;
    overflow-y: hidden;
    overflow-x: scroll;
}

.col-xs-2 {
    width: 60px;
    height: 60px;
    max-width: 60px;
    display: inline-block;
}

.sport-icon {
    height: 55px;
    max-width: 55px;
    display: inline-block;
    text-align: center;
    text-decoration: none;
    border-radius: 2px;
    opacity: 1;
}

.sport-icon a {
    width: 60px;
    height: 60px;
    max-width: 60px;
    padding: 5px;
    display: block;
}

.sport-icon a span.icon-title {
    color: #fff;
    font-size: 8px;
    text-transform: uppercase;
}

.active-sports {
    opacity: 1;
    background-color: var(--color-active) !important;
    display: block;
}

.sport-icon.active-sports span.icon-title {
    color: #ffffff !important;
}

.sport-icon .icon {
    display: block;
}

.sport-icon .icon img {
    width: 25px;
    height: 25px;
}

.navigation-bar {
    padding: 0;
    width: 100%;
    max-width: 540px;
    position: relative;
    background-color: var(--bg-markets);
    display: flex;
    flex-direction: row;
}

.market-games {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    height: 60px;
}

.market-games a {
    padding: 0px 5px;
    color: #fff;
    font-size: 13px;
    text-transform: capitalize;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: 400;
    margin: 0 10px;
    opacity: 0.6;
    height: 45px;
}

.market-games.active-game a {
    color: #ffffff;
    opacity: 1;
}

.market-games img {
    max-width: 25px;
    margin-top: 0;
    width: 25px;
}

.market-games span {
    display: block;
    text-align: center;
    font-size: 12px;
    font-weight: 600;
    color: #fff;
    margin-bottom: 0.5em;
}

.market-games.search-icon {
    top: 0;
    right: 0;
    box-shadow: #27273f 0px 0px 1px 0px;
    background-color: var(--color-secondary);
    text-align: center;
    padding: 10px 0;
}

.nicescroll-rails {
    bottom: 0 !important;
    opacity: 0 !important;
}
#ascrail2000-hr {
    height: 0 !important;
}

#ascrail2000 {
    opacity: 0 !important;
}

.games-nav-wrapper {
    max-width: 540px;
    width: 100%;
    background-color: var(--bg-markets);
    float: left;
    padding-bottom: 0 !important;
    margin-top: -2px;
}

.games-nav-wrapper .page-navigation a {
    color: #ffff;
    text-transform: capitalize;
    padding: 10px 5px;
    font-weight: 600;
    margin: 0;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 13px;
    text-align: center;
    width: 100%;
}

.page-tab-nav {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.page-navigation {
    width: 24%;
}

.page-navigation img {
    width: 20px;
    margin-right: 5px;
}

#popular {
    background-color: #2781e3;
    background: rgb(39, 110, 219);
    background: linear-gradient(90deg, rgba(39, 110, 219, 1) 35%, rgba(36, 160, 237, 1) 100%);
}

#instant {
    background-color: #b842fb;
    background: rgb(185, 65, 251);
    background: linear-gradient(90deg, rgba(185, 65, 251, 1) 51%, rgba(170, 74, 251, 1) 100%);
}

#tables {
    background-color: #e7a63d;
    background: rgb(252, 142, 52);
    background: linear-gradient(90deg, rgba(252, 142, 52, 1) 51%, rgba(227, 173, 62, 1) 100%);
}

#slots {
    background-color: #dc3941;
    background: rgb(237, 53, 67);
    background: linear-gradient(90deg, rgba(237, 53, 67, 1) 50%, rgba(230, 91, 96, 1) 100%);
}

.games-nav-wrapper .page-navigation.active-tag-game a {
    color: #ffffff;
}

.page-nav a {
    border: 2px solid var(--color-active);
    background-color: var(--color-secondary);
    color: #ffff;
    text-transform: capitalize;
    padding: 10px 30px;
    font-weight: 600;
    border-radius: 5px;
    display: inline-block;
    font-size: 13px;
    text-align: center;
}

.tab-nav .page-nav a {
    border: 2px solid var(--color-active);
}

.tab-nav .page-nav.active-page a {
    background-color: var(--color-active);
    color: var(--color-secondary);
    color: #ffff;
}

.page-nav.active-page a {
    background-color: var(--color-active);
}

.footer-nav .page-nav a {
    padding: 5px 7px;
}

.page-nav a img {
    max-width: 15px;
    display: inline-block;
    width: 15px;
}

.page-nav span {
    width: 100%;
    display: inline-block;
    color: #ffff;
    text-transform: capitalize;

    font-size: 12px;
    font-weight: 600;
    letter-spacing: 0.01em;
}

.footer-links a {
    color: #fff;
    border-right: 1px solid #fff;
    font-size: 11px;
}

.footer-links a:last-child {
    border-right: 0;
}

.footer-logo img {
    margin: auto;
}

.other-contacts a {
    color: #ffff;
    font-size: 12px;
    text-align: center;
    display: block;
}

.other-contacts .bi {
    color: var(--color-active);
    font-size: 30px;
}

.age-limit {
    background-color: var(--color-active);
    padding: 10px;
    border-radius: 50px;
    display: inline-block;
    width: 40px;
    height: 40px;
    font-size: 13px;
    line-height: 21px;
    margin-top: 10px;
}

.bottom-footer {
    background-color: var(--color-active);
    display: none;
}

.provider-logo img {
    max-width: 50px;
}

.footer-nav {
    bottom: -5px;
    z-index: 50;
    background-color: var(--color-secondary);
    max-width: 540px;
    width: 100%;
    box-shadow: var(--color-active) 0px 0px 1px 0px;
    clip-path: inset(-10px 0px 0px 0px);
    padding-bottom: 0.5em;
}

.footer-nav .d-flex {
    width: 100%;
}

.footer-nav .d-flex .flex-fill {
    width: 25%;
}

.search-bar .form-control {
    border: 1px solid #ffffff;
}

.search-wrapper a {
    color: #ffffff;
}

.search-game {
    font-size: 14px;
}

.games-wrapper {
    max-width: 540px;
    width: 100%;
    float: left;
    padding-top: 0 !important;
}

.menu-wrapper a {
    color: #ffffff;
    padding: 7px 0;
    display: flex !important;
    justify-content: space-between;
}

.subscriptions .subscription-status {
    border-radius: 4px;
}

.credit-status {
    background: rgba(81, 164, 86, 0.4);
}

.debit-status {
    background: rgba(245, 194, 68, 0.4);
}

.faqs-wrapper .btn {
    padding-left: 0;
    width: 100%;
    text-align: left;
}

.faqs-wrapper .card-body {
    opacity: 0.5;
}

.faqs-tab-section .card-body {
    opacity: 0.5;
    font-size: 0.9em;
}

.faqs-tab-section .btn {
    padding: 0;
    font-size: 1em;
    text-transform: inherit;
}

.summary-section {
    padding-bottom: 5em;
}

.summary-section .card-body {
    padding: 0;
    font-size: 13px;
}

.summary-tab-wrap .flex-fill.nav-item {
    text-align: center;
}

.referral-tab-section p {
    opacity: 0.5;
    padding-bottom: 0;
}

.referred-person-avator {
    border-radius: 50%;
    padding: 10px 10px;
    width: 20px;
    height: 20px;
    line-height: 0px;
    box-shadow: #e2edff 0px 0px 2px 0px;
    font-size: 8px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    text-align: center;
    margin-left: 7px;
}

/* -----Pop Up---- */

#ad-popup-wrapper {
    height: 100vh;
    display: none;
    background: rgba(0, 0, 0, 0.7);
    position: fixed;
    top: 0;
    z-index: 100;
    width: 100%;
    max-width: 540px;
}

#ad-popup-wrapper .close {
    top: 50px;
    right: 30px;
    z-index: 1000;
    border-radius: 50%;
}

#ad-popup-wrapper .card {
    position: absolute;
    width: 100%;
    text-align: center;
    max-width: 350px;
    margin: auto;
    left: 0;
    right: 0;
}

#ad-popup-wrapper img {
    display: inline-block;
}

.ad-image img {
    width: 100%;
    max-width: 350px;
}

.popup-btn img {
    max-width: 150px;
}

button {
    background: #000;
    color: #fff;
    text-align: center;
    font-weight: bold;
    padding: 10px 30px;
    border-radius: 3px;
}

.overlay {
    position: fixed;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    z-index: 100;
}

#popup {
    max-width: 520px;
    width: 100%;
    padding: 0;
    position: absolute;
    background-color: #27273f;
    margin: 5px auto 0;
    bottom: 10px;
    left: 0;
    right: 0;
}

#popup h1 {
    font-size: 17px;
    font-weight: 800;
}

.close {
    position: absolute;
    top: -1px;
    right: 14px;
    cursor: pointer;
    color: #ffffff;
    font-size: 30px;
    z-index: 100;
}

.home-icon {
    text-align: right;
}

.home-icon .bi {
    color: #fff;
    font-size: 25px;
}

.home-icon img {
    width: 30px;
}

.join-logo img {
    max-width: 150px;
}

.join-logo {
    margin: 0;
}

#header {
    width: 100%;
    max-width: 540px;
    float: left;
    /*position: fixed;*/
    z-index: 100;
    /*height: 4em;*/
}

.form-check a u {
    color: #ddd;
}

button.gradient-custom-4 {
    color: #ffffff !important;
    font-size: 1.3em;
    height: calc(1.5em + 1.5rem + 2px);
}

form .btn-primary:hover {
    color: #212529 !important;
    background-color: var(--color-active);
    border-color: var(--color-active);
    -webkit-transition: all 0.5s ease 0s;
    transition: all 0.5s ease 0s;
}

.code-form-wrap {
    width: 100%;
    display: inline-block;
    margin-bottom: 1em;
    background-color: transparent;
    background-clip: padding-box;
    border: 1px solid var(--color-active);
    padding: 0.2em;
    border-radius: 0.3em;
}

.code-form-wrap a {
    color: #ffffff;
}

.code-form-wrap a:hover {
    color: #ffffff;
}

.code-form {
    margin: 0;
    border: none;
    font-size: 13px;
    background-color: transparent !important;
    color: #ffffff !important;
    padding: 12px;
    display: inline-block;
    height: calc(1em + 1.5rem + 10px);
    width: 70%;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-transition: all 0.5s ease 0s;
    transition: all 0.5s ease 0s;
}

.submit-code {
    margin: 0;
    height: 48px;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 11px;
    line-height: 38px;
    background-color: var(--color-active) !important;
    color: #ffffff;
    padding: 5px 10px;
    width: 28%;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-transition: all 0.5s ease 0s;
    transition: all 0.5s ease 0s;
    border-radius: 0.3em;
    cursor: pointer;
    float: right;
    text-align: center;
}

.alert-message {
    color: #00ee6f;
    font-size: 13px;
    margin-bottom: 2em;
}

.other-verification-option {
    border-radius: 5px;
    background-color: #191b27;
    padding: 2em;
}

.other-verification-option h2 {
    color: var(--color-active);
    font-weight: 600;
    font-size: 1.5em !important;
}

.onboarding {
    width: 100%;
    float: left;
}

.onboarding p {
    font-size: 15px;
}

.onboarding p.alert-message {
    font-size: 13px;
}

.success-icon-wrap {
    background-color: #1bb37d;
    background-color: rgba(27, 179, 125, 0.5);
    width: 90px;
    height: 90px;
    position: relative;
    border-radius: 50%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin: auto;
    margin-bottom: 2em;
}

.success-icon {
    background-color: rgba(27, 179, 125, 0.9);
    width: 60px;
    height: 60px;
    position: absolute;
    border-radius: 50%;
    display: flex;
    flex-direction: row;
    align-items: center;
}

.success-icon .bi {
    font-size: 36px;
    color: #ffffff;
    margin: auto;
}

.footer {
    float: left;
    padding-bottom: 0 !important;
}

.license {
    margin-bottom: 0 !important;
}

.page-nav.active-tag-game a {
    border: 2px solid var(--color-active);
    background-color: var(--color-active);
    color: #fff;
}

.contacts h6 {
    font-weight: 600;
}

.license h6 {
    font-weight: 600;
}

.license p {
    font-size: 11px;
    line-height: 14px;
    font-weight: 400;
}

.responsible-age {
    display: flex;
    flex-direction: column;
}

.market-games.search-icon a {
    margin: 0;
    opacity: 1;
}

.choose-amount-to-deposit button span {
    display: block;
    text-align: center;
    font-weight: 600;
    font-size: 13px;
}

.jackpot-wrapper {
    width: 100%;
    float: left;
}

.jackpot-tab {
    width: 100%;
    float: left;
    background-color: var(--color-secondary) !important;
}

.jackpot-tab a {
    color: #ffff;
    font-size: 1em;
    padding: 10px 0 !important;
}

.jackpot-tab a:hover {
    color: #ffff;
    opacity: 0.6;
}

.jackpot-display {
    width: 100%;
    float: left;
}

.jackpot-timer-wrapper{
    background-color: var(--bg-markets);
    display: flex;
    overflow: hidden;
    outline: none;
    cursor: grab;
    touch-action: none;
    width: 100%;
}

.jackpot-timer-wrapper .scrollmenu{
    display: flex;
    cursor: grab;
    touch-action: none;
}

.jackpot-timer{
    padding-inline: 5px;
}

.jackpot-timer a{
    color: #FFF;
    display: flex;
    flex-direction: column;
    text-align: center;
    padding: 5px;
}

.jackpot-timer.active-jackpot a{
    padding: 5px;
    background-color: #CF2029;
}

.jackpot-text{
    font-weight: 600;
    font-size: 11px;
    padding: 1px 0;
}

.jackpot-time{
    font-size: 10px;
}

.jackpot-time .date{
    font-size: 10px;
}

.jackpot-time .time{
    font-size: 11px;
}

.all-trasaction-done {
    color: var(--color-active);
    font-size: 13px;
}

.jackpot-games {
    width: 100%;
    float: left;
    padding-top: 5px;
}

.team-selection-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0;
}

.team-selection {
    width: 100%;
    display: flex;
    flex-direction: row;
}

.league-display-wrapper {
    font-size: 1.2em;
    margin-bottom: 10px !important;
    width: 100%;
    float: left;
    color: #ffffff;
}

.match-selection {
    float: left;
    width: 100%;
    display: inline;
}

.single-match {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    padding: 2px 0;
}

.playing-teams-wrap {
    display: flex;
    align-items: center;
    flex-direction: column;
    padding-top: 3px;
}

.playing-teams-wrap a {
    align-items: center;
    flex-direction: column;
    justify-content: flex-start;
}

.playing-teams {
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0em;
    text-align: left;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border: none;
    padding-top: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    font-size: 15px;
    padding-left: 3px;
}

.playing-teams span {
    font-style: normal;
    font-weight: 400;
    line-height: 15px;
    font-size: 15px;
    text-align: left;
    color: var(--color-betslip);
    display: flex;
    align-items: center;
    letter-spacing: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.playing-teams span.remove-bet {
    margin-left: -10px;
    position: absolute;
    top: -10px;
}

.playing-teams .col-2 .final-odds-applied {
    text-align: center;
    display: flex;
    justify-content: center;
    color: var(--bg-active);
    font-size: 16px;
}

.odd-selection {
    background: var(--bg-odd-button);
    border-radius: 2px;
    border: 1px solid var(--border-color-odd-button);
    padding: 3px 0;
    font-size: 10px;
    font-weight: 600;
    margin: 2px;
    display: flex;
    flex-direction: column;
}

.btn-odd-option {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-right: 1em;
}

.red-txt {
    color: #d32027;
    font-size: 20px;
}

.green-up .bi {
    color: green;
    margin-left: -3px;
}

.ods-given {
    width: 100%;
    padding: 5px 0;
    color: var(--color-odds);
    font-size: 1.5em;
    font-weight: 600;
}

.ods-title {
    width: 100%;
    top: 6px;
    color: var(--color-odds-title);
    font-size: 16px;
    opacity: 0.3;
}

.possible-win {
    max-width: 540px;
    width: 100%;
    min-height: 50px;
    background-color: var(--color-secondary);
    color: #ffff;
    font-family: "Cabin Condensed", sans-serif;
    font-style: normal;
    font-size: 13px;
    text-transform: capitalize;
    mix-blend-mode: normal;
    padding: 0;
    position: relative;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
}

.possible-win a span.possible-payout {
    color: #202020;
}

.ods-title .bi {
    display: none;
}

.league-display-wrapper span {
    font-weight: 600;
}

.place-bet {
    display: block;
    text-align: center;
}

.place-bet a {
    background-color: var(--color-active);
    border-radius: 2px;
    color: #fff;
    font-size: 17px;
    display: block;
    padding: 10px 2em;
    width: 100%;
}

.remove-expired-markets {
    margin-bottom: 0.5em;
}

.remove-expired-markets button {
    background-color: var(--color-active);
    border-radius: 2px;
    color: white;
    font-size: 13px;
    width: 100%;
    border: none;
    text-transform: capitalize;
    padding: 5px 10px;
}

.footer-logo {
    margin-bottom: 0;
    width: 100%;
}

.footer-logo a {
    font-size: 24px;
}

.total-odds, .combinations {
    font-weight: 600;
}

.jackpot-game-selected {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    padding: 3px 5px;
    border: 1px solid #fff;
}

.odd-selection.added-game-btn {
    background-color: var(--bg-odd-button-active);
    border: 1px solid var(--border-color-odd-button-active);
}

.odd-selection.added-game-btn .ods-title {
    color: var(--color-odds-title-active);
    opacity: 1;
}

.odd-selection.added-game-btn .ods-given {
    color: var(--color-odds-active);
}

.rewards-wrapper {
    padding-bottom: 8em;
    width: 100%;
    float: left;
}

.terms-and-conditions-header {
    padding: 10px;
    padding-bottom: 0;
}

.Freebets-terms {
    color: #ffffff;
    padding: 2%;
    margin-bottom: 1em;
    padding-top: 0;
}

/* -------------------accordion panel------------------- */

.accordion-panel-wrapper{
    background-color: var(--bg-market-wrap-even);
    border-radius: 10px;
}
.panel-heading {
    padding: 0;
     border:0;
}
.panel-title {
    display:block;
    padding: 10px;
    color:#ffffff;
    font-size: 14px;
    font-weight: normal;
    text-transform: initial;
    word-spacing: 0;
    text-decoration:none;
}

.panel-title a{ color:#ffffff;
    opacity: 0.7;
    display: block;
}

.panel-heading.active .panel-title a{
    color: var(--color-active);
    opacity: 1 !important;
}
  
.panel-heading  a:before {
content:url("../icons/arrow-down.svg");
width: 16px;
float: right;
transition: all 0.5s;
filter: hue-rotate(11deg);
opacity: 0.5;
}

.panel.panel-default{
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}


.panel-heading.active a:before {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    transform: rotate(180deg);
}

.panel-body{
padding: 10px;
font-size: 14px;
line-height: 18px;
}

  /* -------------------End Of accordion panel------------------- */
.account-access.account-balance-wrap a {
    font-weight: 400;
    padding: 7px 5px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-radius: 5px !important;
}

.free-game-to-pla {
    position: relative;
}

.free-game-content {
    top: 2em;
    margin: 1em auto;
    text-align: center;
    width: 90%;
    color: #ffffff;
    left: 0;
    right: 0;
}

.free-game-content h1 {
    font-size: 2.5em;
    font-weight: 900;
    margin-bottom: 0.2em;
}

.free-game-content h1 span {
    display: block;
    font-weight: 200;
}

.free-game-content h6 {
    font-size: 1.5em;
    margin-bottom: 0.5em;
}

.free-game-content a {
    background-color: #1cb675;
    color: #ffffff;
    font-size: 1.1em;
    padding: 1em 1em;
    border-radius: 5px;
    text-transform: uppercase;
    font-weight: 600;
    display: inline-block;
}

.free-bets {
    padding-bottom: 2em;
    float: left;
    width: 100%;
}


.Freebets-terms .accordion > .card > .card-header {
    border-bottom: 1px solid var(--color-active);
}

.accordion > .card > .card-header h3 {
    padding: 0.5em 0;
}

.Freebets-terms .accordion > .card {
    border-bottom: 1px solid #212335;
}

.Freebets-terms li {
    padding-bottom: 0.7em;
    color: #ffffff;
    font-size: 13px;
}

.Freebets-terms h3 {
    line-height: 1.5em;
    font-size: 13px;
    color: #ffffff;
}

.Freebets-terms p {
    line-height: 1.5em;
    font-size: 12px;
    color: #fff;
    opacity: 0.5;
}

.Freebets-terms h6 {
    line-height: 1.5em;
    font-size: 13px;
    color: #ffffff;
}

.terms-and-conditions {
    color: #ffffff;
}

.terms-and-conditions h2{font-size: 1.28em;}

.terms-and-conditions .accordion > .card > .card-header h3{
    color: var(--color-active) !important;
}

.game-action-btn {
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 20px;
}

.game-action-btn a {
    width: 50%;
    padding: 10px;
    font-size: 1em;
}

.account-balance-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.account-balance-wrap span.account-bal {
    display: inline-block;
    text-align: center;
    padding: 0 4px;
}

.account-balance-wrap span.account-bal img {
    width: 25px;
    opacity: 0.5;
}

.account-balance-wrap a {
    background-color: var(--color-active);
    border: 2px solid var(--color-active) !important;
    padding: 7px 10px;
}

.leaderboard {
    float: left;
    padding-inline: 3%;
}

.leaderboard .page-link{
    border: 1px solid var(--color-active) !important;
    color: #FFF;
    background-color: transparent;
}

.leaderboard .page-link.active{
    background-color: var(--color-active);
}

.leaderboard-table{
    width: 100%;
    padding: 1%;
    border-radius: 10px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    background-color: var(--bg-market-wrap-even);
    padding-bottom: 0;
}

.leaderboard-heading{
    font-size: 1.28em;
}

.pagination-wrap{
    width: 100%;
    padding: 1%;
    border-radius: 10px;
    margin-bottom: 10px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    background-color: var(--bg-market-wrap-even);
}

.terms-and-conditions-wrap{
    width: 100%;
    padding: 1%;
    border-radius: 10px;
    margin-bottom: 10px;
    background-color: var(--bg-market-wrap-even);
}

.modal-dialog {
    max-width: 512px;
    margin-top: 0;
}

.modal-content {
    background-color: var(--color-accent);
    margin-top: 10px;
}

.close {
    opacity: 1;
}

.modal-header .close .bi{
    margin-right: -15px;
}

.bi-x-circle::before {
    content: "\f623";
    color: #fff;
}

.modal-body {
    padding-top: 0px;
    padding-inline: 0.5rem;
    padding-bottom: 0;
}

.choose-amount {
    color: #ffffff;
}

.account-successful-created h1 {
    color: #ffffff;
    font-size: 2em;
    padding-bottom: 1em;
}

.account-successful-created img {
    width: 55px;
}

.welcome-bonus a,
.welcome-freebets a {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.welcome-bonus {
    margin-bottom: 2em;
}

.account-successful-created {
    border-radius: 5px;
    background-color: #191b27;
    padding: 2em 1em;
}

.welcome-details h2 {
    font-size: 1.3em !important;
    color: #00ee6f !important;
}

.account-successful-created p {
    color: #ffffff;
    opacity: 0.7;
    font-size: 13px;
}

.account-successful-created .welcome-details {
    max-width: 350px;
}

.welcome-to-offer-page .bi {
    color: #fff;
}

.welcome-bonus .welcome-details {
    border-bottom: 1px solid #494c61;
}

.welcome-icon {
    padding-inline: 0.4em;
}

.welcome-to-offer-page {
    padding-inline: 1em;
}

.card-header-tabs {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    margin-bottom: 0;
}

.card-header-tabs li.nav-item {
    width: 50%;
    text-align: center;
    border-bottom: 1px solid var(--color-active);
}

.nav-tabs .nav-link:hover,
.nav-tabs .nav-link:focus {
    border-color: transparent;
}

li.nav-tabs .nav-item {
    width: 100%;
    text-align: center;
}

.nav-tabs a.nav-link {
    color: #ffffff !important;
    font-size: 1.3em;
}

.nav-tabs .nav-link.active {
    background-color: var(--color-active);
    color: #ffffff;
}

.deposit-wrapper .card {
    width: 100%;
}

.choose-amount-to-deposit {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    max-width: 768px;
    width: 100% !important;
    margin: auto;
    margin-bottom: 5px;
}

.choose-amount-to-deposit .col-12{
    display: flex;
    flex-wrap: wrap;
    gap: 5px 5px;
    width: 100%;
}

.choose-amount-to-deposit .btn {
    font-size: 13px;
    flex: 1 0 60px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #ffff;
    border: 2px solid var(--color-active) !important;
    border-radius: 4px;
}

.choose-amount-to-deposit .form{
    display: flex;
    flex-wrap: wrap;
    gap: 5px 5px;
    width: 100%;
}

.choose-amount-to-deposit .form button{
    flex: 1 0 50px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

a.place-bet {
    background-color: var(--bg-placebet-button);
    color: #ffffff;
    padding: 15px 0px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 1.4em;
}

/* ---------------League---------------------------- */

.app-countries-icons {
    width: 100%;
    max-width: 540px;
    white-space: nowrap;
    overflow-x: hidden;
    overflow-y: hidden;
    background: var(--color-primary);
}

.league-countries {
    width: 100%;
    padding-top: 0.5em;
    padding-bottom: 0.5em;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.country-flag-icon {
    display: flex;
    justify-content: center;
    flex-direction: row;
    color: white;
    text-align: center;
    text-decoration: none;
    opacity: 0.3;
}

.country-flag-icon.active-league {
    opacity: 1;
}
.league-countries .country-flag-icon {
    flex: 1 1;
}

.country-flag-icon .icon img {
    width: 30px;
    margin: 0 auto 7px;
}

.country-flag-icon a {
    color: #fff;
}

.country-flag-icon a span {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.country-name {
    text-transform: uppercase;
    font-size: 0.8em;
    font-weight: 600;
}

.app-market-results-standing {
    width: 100%;
    max-width: 768px;
    background-color: var(--color-accent);
}

.tab {
    overflow: hidden;
    padding: 0 0;
    float: left;
    width: 100%;
    background-color: var(--color-secondary);
}

.tab li a.active-tab {
    opacity: 1;
    padding: 5px 16px;
    background-color: var(--color-secondary);
    border-radius: 4px;
    color: #fff;
}
.tab a {
    float: none;
    border: none;
    outline: none;
    cursor: pointer;
    transition: 0.3s;
    font-family: "Cabin Condensed", sans-serif;
    font-size: 1.3em;
    text-transform: capitalize;
    color: #ffffff;
    text-align: center;
    text-decoration: none;
    font-weight: 400;
}

.tab-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding-left: 4px;
    padding-right: 4px;
}
.tab-details {
    width: 100%;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #2c3e50;
}

.tab-details:last-child{
    border-right: 0;
    display: flex;
    align-items: center;
}

.tab-details a {
    display: block;
    opacity: 1;
    font-size: 1.1em;
}

.tab-details a.active-tab {
    opacity: 1;
    color: var(--color-active);
}

.tab-details.my-bets {
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: center;
}

.tab-details.my-bets span {
    display: flex !important;
    justify-content: center;
    font-size: 1em;
    padding: 0 1px;
}

.tab-details.my-bets a {
    opacity: 1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.tab-details.my-bets img {
    width: 20px;
}

.active-tab-jackpot {
    opacity: 1;
    color: var(--color-active) !important;
    border-bottom: 2px solid var(--color-active);
    padding: 10px 0 !important;
}

.app-bet-time {
    width: 100%;
    max-width: 768px;
    white-space: nowrap;
    overflow-y: hidden;
    overflow-x: scroll;
    display: flex;
    flex-direction: row;
    justify-content: start;
    background-color: var(--color-primary);
    padding-bottom: 0;
    margin-bottom: 1px;
}

.app-bet-time ul.time {
    width: 100%;
    background-color: var(--color-accent);
    padding-left: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    flex-grow: 0;
    gap: 0.1em;
}

.app-bet-time li.next-game-time {
    color: #fff;
    padding: 0.75em;
    font-size: 16px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: capitalize;
    background-color: var(--color-primary);
    flex-shrink: 0;
    flex-grow: 0;
}

.app-bet-time ul.time li:first-child {
    margin-left: 0.75px;
}

.market-display {
    width: 100%;
    float: left;
}

.league-games-wrapper {
    width: 100%;
    float: left;
}

.timer {
    color: #fff;
    background-color: #f00;
    font-weight: 600;
    padding: 10px 15px;
    font-size: 13px;
    text-align: center;
}

.match-number-wrap .row {
    align-items: center;
}

.match-number-wrap {
    background-color: #f00;
    width: 100%;
    padding-left: 15px;
    padding-right: 15px;
    padding-top: 5px;
    padding-bottom: 5px;
    margin-top: -2px;
    height: 24px;
}

.match-number-wrap span {
    padding: 2px 0;
    color: #ffffff;
    font-size: 13px;
    font-weight: 700;
}

.match-number-selected {
    color: var(--bg-active) !important;
}

.market-option {
    float: left;
    width: 100%;
    background-color: var(--bg-markets);
    padding: 0.2em 0 0.2em;
    display: flex;
    align-items: center;
}

.tabcontent {
    padding: 0 1em !important;
    border-top: 2px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sport_dropdowns {
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    white-space: nowrap;
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: 5px;
    width: 100%;
}

.sport_dropdowns button.btn {
    border-radius: 50px;
    color: var(--color-market-button);
    background-color: var(--bg-market-button);

}
.btn.btn-warning {
    background-color: var(--bg-active) !important;
    border-color: transparent;
    color: var(--color-secondary) !important;
    font-weight: 900;
}

.sport_dropdowns button {
    font-size: 1em;
    font-style: normal;
    font-weight: 600;
    text-align: left;
    text-transform: uppercase;
}

.sport_dropdowns button a.other-options {
    opacity: 1;
    padding: 7px 10px;
    background-color: var(--color-secondary);
    border-radius: 3rem;
    color: #fff;
}

.page-nav-wrapper {
    bottom: 0;
    z-index: 100;
    background-color: var(--color-secondary);
    max-width: 540px;
    width: 100%;
    padding-bottom: 0 !important;
}

.share-bet-stake-and-wrap {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    margin: 0;
    padding: 0;
}

.share-bet-stake-and-wrap .col-8 {
    padding-inline: 0;
    padding-left: 5px;
}

.share-bet-stake-and-wrap .col-4 {
    padding-right: 15px !important;
}

.stake-input input::-webkit-input-placeholder {
    direction: rtl;
    text-align: left;
    color: #fff;
    font-size: 13px;
}

.stake-input {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 18px;
    padding: 0px 5px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.stake-input input.form-control {
    font-size: 16px;
    border: transparent;
    color: #2c3e50 !important;
    background-color: #fff !important;
    height: 33px;
    border-radius: 5px;
}

.my-slip-teams {
    border: 1px solid var(--bg-active);
    background-color: var(--bg-active) !important;
    color: var(--bg-markets);
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    border-radius: 30px;
    padding: 14px 10px;
    font-size: 1.2em;
    font-weight: 900;
}

.my-teams {
    background-color: var(--bg-active);
    border-radius: 50px;
    padding: 5px;
    color: var(--bg-markets);
    width: 25px;
    height: 25px;
    text-align: center !important;
    font-size: 13px;
    line-height: 15px;
    font-weight: 500;
}

.my-teams a {
    color: white;
    font-size: 16px;
}

.share-bet a {
    background-color: var(--color-active);
    border-radius: 2px;
    color: #fff;
    font-size: 13px;
    display: block;
    padding: 10px;
    text-align: center;
    width: 100%;
    font-weight: 400;
    line-height: 30px;
}

.league-wrapper {
    width: 100%;
    float: left;
    background-color: #ffffff;
    padding-bottom: 2em;
    min-height: 83vh;
}

.league-wrapper .matches-wrapper {
    padding-top: 0 !important;
}

.standing-wrapper {
    width: 100%;
    float: left;
    background-color: var(--color-active);
    border-bottom: 1px solid var(--color-active);
    font-weight: 400;
    color: #fff;
    font-size: 1em;
    text-transform: capitalize;
}

.standing-heading {
    color: #fff;
    font-size: 1.3em;
}

.green-txt {
    color: #37b34a;
    font-size: 20px;
}

.black-txt {
    color: black;
    font-size: 20px;
}

.live-match-selection {
    border-bottom: 1px solid #f1f1f1;
    font-size: 1.13em;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.live-match-selection .col-6 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0 !important;
    /* margin-bottom: 2px; */
}
.live-match-selection a {
    display: flex;
    align-items: center;
}

.home-team,
.away-team {
    white-space: nowrap;
    max-width: 90px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    line-height: 10px;
}

.team-jersey img {
    width: 20px;
    transform: scale(1.5);
    padding-top: 10px;
    padding-bottom: 10px;
}

.warning-signal {
    width: 100%;
    padding-top: 2px;
    margin-bottom: -5px;
}

.warning-signal span {
    width: 100%;
    display: block;
    font-weight: 600;
    font-size: 12px;
    padding: 2px 5px;
    padding-right: 15px;
    padding-left: 15px;
}

.warning-signal span.bg-success {
    color: #ffffff;
}
.warning-signal span.bg-warning {
    color: #ffffff;
}
.warning-signal span.bg-light {
    color: var(--color-secondary);
}

.standing-heanding {
    background-color: #343a40;
    color: #fff;
    padding: 0.75em 0;
}

.match-standing {
    border-bottom: 1px solid #f1f1f1;
}

.match-standing-wrapper {
    padding-bottom: 0;
}

select::-ms-expand {
    display: none;
}

select {
    -webkit-appearance: none;
    -moz-appearance: none;
    -ms-appearance: none;
    appearance: none;
    outline: 0;
    background: transparent;
    background-image: none;
    width: 100%;
    height: 100%;
    margin: 0;
    padding-left: 1em;
    color: #fff;
    cursor: pointer;
    font-size: 1.1em;
    line-height: initial;
    appearance: none;
    outline: 0;
    border: none;
}

.select {
    position: relative;
    display: flex;
    width: 100%;
    height: 2em;
    line-height: 3;
    background: var(--bg-active);
    overflow: hidden;
    border-radius: 1em;
    font-size: 16px;
}

.select.active-select {
    background-color: var(--color-active);
}

.select::after {
    content: "\25BC";
    position: absolute;
    top: 0px;
    right: 0;
    bottom: 0;
    padding: 0 01em;
    background: transparent;
    color: #fff;
    pointer-events: none;
    font-size: 10px;
}

.select:hover::after {
    color: var(--bg-markets);
}

.select::after {
    -webkit-transition: 0.25s all ease;
    -o-transition: 0.25s all ease;
    transition: 0.25s all ease;
}

.page-header {
    width: 100%;
    float: left;
    background: var(--color-secondary) !important;
}

.page-header .col-12{
    display: flex;
    align-items: center;
}

.selected-team {
    font-weight: 600;
}
.remove-bet a {
    font-size: 1.8em;
    color: var(--color-betslip);
}

.playing-teams .row,
.team-to-win-selected .row,
.team-to-win-odds .row {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.team-to-win-selected {
    margin-bottom: 5px;
    width: 100%;
    font-size: 1.13em;
}

.page-heading {
    width: 100%;
    text-transform: capitalize;
    font-size: 16px;
    background-color: var(--color-accent);
}

.my-bets-wrapper {
    margin-right: 5px;
    margin-left: 5px;
    padding-top: 5px;
}

.bet-outcome-result.text-success{
    font-size: 15px;
}

.know-about-bets{
    text-align: center;
    color: #FFF;
    background: var(--color-accent);
    padding: 10px;
    border-radius: 5px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    font-size: 16px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    border-bottom: 1px solid var(--color-accent);
}

.know-about-bets .badge{
    width: 100%;
    line-height: 1.5;
    font-size: 11px;
}

.bets-tab-scroll{display: flex;flex-direction: row;white-space: nowrap;overflow-y: hidden;overflow-x: scroll;width: 100%;}

.my-bets-wrapper .nav-tabs {
    border-bottom: none;
    margin-bottom: 0;
    background: var(--color-secondary);
    display: flex;
    flex-wrap: wrap;
    gap: 1px;
    width: 100%;
}

.my-bets-wrapper .nav-tabs .nav-item {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    display: flex;
    flex: 1 1;
}

.my-bets-wrapper .nav-tabs .nav-item button {
    width: 100%;
    border-radius: 0;
    background-color: #16202c;
    background: #363d48;
    text-transform: capitalize;
    font-size: 14px;
    font-weight: 400;
}

.my-bets-wrapper .nav-tabs .nav-item button.active {
    background-color: var(--color-active);
}

.my-bets-wrapper .nav-tabs .nav-item button:focus {
    border-color: transparent;
}

.mybet-wrapper {
    width: 100%;
    float: left;
    background: var(--color-accent);
    padding: 10px 0 0;
    border-radius: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.outcome-signs-wrap {
    color: #fff;
    display: flex;
    justify-content: space-between;
    display: flex;
    flex-wrap: wrap;
    gap: 5px 5px;
    width: 100%;
}

.single-bet-outcome-wrapper{
    padding: 0;
}

.single-bet-outcome-wrapper .bet-outcome{
    padding: 0;
    margin-bottom: 0;
}

.single-bet-outcome-wrapper .single-bet-outcome .bet-outcome{
    border-bottom:0
}

.single-bet-outcome-wrapper .single-bet-outcome {
    width: 100%;
    font-weight: 400;
    color: #fff;
    font-size: 1em;
    display: flex;
    padding: 0.5em 1em;
    border-radius: 0;
    box-shadow: none !important;
    border-bottom: 1px solid #F0F0F0;
}

.single-bet-outcome-wrapper .single-bet-outcome:last-child{
    border-bottom:0
}

.single-bet-outcome a {
    color: var(--color-betslip);
}

.bet-outcome-wrap {
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 1em;
    background: var(--color-accent);
    margin-right: 10px;
    margin-left: 10px;
    padding: 10px;
    border-radius: 5px;
}

.bet-outcome {
    width: 100%;
    float: left;
    color: var(--color-betslip);
    border-bottom: 1px solid #2d4458;
    background-color: var(--bg-betslip);
    margin-bottom: 1px;
    padding: 0.5em 1em;
}

.bet-outcome-wrap .bet-outcome:last-child{
    border-bottom: 0;
}

.single-game-bet-id {
    padding-top: 0.5em;
    padding-bottom: 0.5em;
    font-size: 0.9em;
}

.game-final-outcome{
    color: var(--color-betslip-market);
}

.bet-id {
    padding: 0;
}

.game-playing{
    font-weight: 900;
    font-size: 15px;
}

.playing-teams span.type-selection{
    display: flex;
    align-items: center;
    padding: 5px 0;
}

.playing-teams span.game-playing{
    font-size: 16px;
}

.playing-teams span.type-selection .team-chosen,
.playing-teams span.type-selection .game-chose-outcome{
    color: var(--color-betslip-market);
}

.playing-teams span.type-selection .game-chose-outcome{
    padding-left: 10px;
}

.bet-outcome-won{
    border-left: 10px solid var(--color-active);
}

.bet-outcome-pending{
    border-left: 10px solid #ffc107;
}

.bet-outcome-cancelled{
    border-left: 10px solid #17a2b8;
}

.bet-outcome-lost{
    border-left: 10px solid #ffffff;
}


.bet-id-and-game-time{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 0.5em;
}

.outcome-signs{
    text-transform: capitalize;
    font-size: 1.1em;
    flex: 1 0 50px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.scanner-wrapper {
    width: 100%;
    float: left;
    text-align: center;
}

.scanner {
    margin: auto;
    max-width: 200px;
    text-align: center;
}

.scanner img {
    border-radius: 10px;
    display: block;
    width: 100%;
}

.faqs-wrapper {
    width: 100%;
    float: left;
}

.referral-form-wrap .card-header-tabs li.nav-item {
    display: flex;
    flex: 1 1;
    justify-content: center;
    text-align: center;
    border-bottom: 1px solid var(--bg-markets);
}

.referral-form-wrap .nav-tabs a.nav-link {
    font-size: 13px;
    padding: 7px;
    font-weight: 600;
    width: 100%;
    text-transform: capitalize;
}

.referral-form-wrap .tab-content {
    color: #fff;
}

.referral-form input.form-control{
    border-top-right-radius:0;
    border-bottom-right-radius:0
}

.referral-form button{
    border-top-right-radius:4px;
    border-bottom-right-radius:4px
}

.app-content {
    width: 100%;
    float: left;
    margin-top: 1em !important;
}

.account-links a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 0 !important;
}

.account-icon {
    display: inline-flex;
    align-items: center;
}

.account-icon img {
    width: 20px;
    margin-right: 5px;
}

.referral-form-wrap {
    width: 100%;
    float: left;
}

.place-bet-wrap {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px !important;
}

.jackpot-wrapper .clear-all-bet {
    justify-content: center;
}

.clear-all-bet{
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.clear-all-bet .bi {
    color: #fff;
    font-size: 1.4em;
}

.clear-all-bet span {
    color: #fff;
    font-size: 1em;
}

.how-to-play-jackpot {
    color: #fff;
    font-size: 12px;
    padding-bottom: 8rem;
    font-weight: 400;
    line-height: 16px;
}

.jackpot-results-outcome {
    width: 100%;
    float: left;
    padding-bottom: 8rem;
}

.betslip-wrapper {
    margin-left: 5px;
    margin-right: 5px;
    border-radius: 5px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    border-bottom: 1px solid var(--bg-betslip);
    background-color: var(--bg-betslip);
    margin-top: 5px;
    /*max-height: 60vh;*/
    overflow-y: scroll;
    padding-bottom: 1em;
}

.bet-outcome-wrap .container{
    padding-inline: 0;
}

.betslip-wrapper .betslip {
    width: 100%;
    padding: 0.5em 0;
    padding-bottom: 0;
    font-weight: 400;
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    font-size: 13px;
    position: relative;
    border-bottom: 1px solid #2d4458;
}

.betslip-wrapper .betslip:last-child{
    border-bottom:0
}

.betslip .playing-teams{
    padding-bottom: 10px;
    position: relative;
}

.underline_slip {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    border-bottom: 1px solid #2d4458;
}

.game-number {
    display: flex;
    justify-content: center;
    flex-direction: row;
    align-items: center;
    align-content: start;
    width: 30px;
}

.game-number-dtls {
    display: flex;
    justify-content: start;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.team-number {
    width: 20px;
    padding-top: 5px;
}

.team-number-selected {
    width: 100%;
}

.total-wrap {
    font-size: 17px;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px !important;
    font-weight: 600;
}

.accordion_container {
    width: 100%;
    font-size: 13px;
}

.accordion_head {
    color: white;
    cursor: pointer;
    font-size: 15px;
}

.accordion_body {
    background: transparent;
}

.accordion_body p {
    padding: 18px 5px;
    margin: 0px;
}

.plusminus {
    float: right;
    width: 5px;
}

.table-heading-row{
    width: 100%;
    font-weight: bold;
    color: var(--color-active);
    font-size: 14px;
}

.referred-person-position{
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 20px;
}

.referred-person-position img {
    width: 20px;
}

.referral-profile h6 {
    font-size: 13px;
    margin-bottom: 5px;
}

.faqs-tab-section {
    padding-bottom: 13em;
}

.menu-wrapper {
    margin-bottom: 1em;
    float: left;
    width: 100%;
}

.menu-wrapper h5 {
    color: #24ac7b;
    font-size: 16px;
}

.referral-tab-section .table-striped .table-header {
    background-color: var(--bg-markets);
}

.referral-tab-section .table thead th {
    border-bottom: 0;
    color: #ffffff;
    font-weight: 600;
}

.referral-tab-section .table thead th:first-child {
    border-right: 1px solid #fff;
}

.referral-tab-section .table th,
.table td {
    color: #ffffff;
    padding-left: 5px;
}

.referral-tab-section .table {
    font-size: 11px;
    border: 1px solid #fff;
}

.referral-tab-section .table th:first-child {
    border-right: 1px solid #343a40;
    width: 25px;
    padding: 10px 15px !important;
}

.referral-tab-section .table td:nth-child(3) {
    padding: 10px 5px !important;
    width: 55px;
    text-align: left;
}

.my-slip-teams {
    width: 50px;
    height: 50px;
}

.my-bet-slip {
    width: 76px;
    text-align: center;
    font-size: 1.13em;
}

.my-total {
    text-align: center;
    padding-right: 10px;
    font-size: 1.13em;
}

.btn-wrapper {
    appearance: none;
    opacity: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    flex: 1 1;
    margin-top: 0px;
}

.game-image {
    position: relative;
    cursor: pointer;
}

.play-now-btn {
    width: 100%;
    padding: 10px;
    border-bottom-left-radius: 0.5rem !important;
    border-bottom-right-radius: 0.5rem !important;
    background-color: var(--color-active);
    font-size: 10px;
    font-weight: 600;
    color: #fff;
    text-align: center;
    bottom: 0;
    z-index: 10;
    position: absolute;
    display: none;
}

.onboarding .card-body h2 {
    color: #00ee6f;
    font-size: 1.4em !important;
}

.account-balance-wrap .login-nav a.deposit-funds {
    background-color: var(--color-active);
    color: #fff;
}

.classic-tabs {
    padding-right: 15px;
    padding-left: 15px;
    margin-top: 0;
}

.classic-tabs ul.nav {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
}

.classic-tabs ul.nav li {
    width: 33%;
    text-align: center;
}

.classic-tabs ul.nav li a {
    color: #fff;
    font-weight: 600;
    letter-spacing: 0.2px;
    text-transform: uppercase;
    border-radius: 0.1rem;
    padding: 0.8rem 0.6rem;
}

.classic-tabs ul.nav li a.active {
    background-color: var(--color-active);
    color: #ffffff;
}

.team-to-win-odds {
    width: 100%;
}

.won-amount {
    font-weight: 900;
}

.promotions-wrapper {
    max-width: 540px;
    width: 100%;
    float: left;
}

.product-promotion {
    padding-bottom: 1em;
}

.promo-description {
    margin: 1em 0;
    color: #fff;
}

.promo-description h2 {
    font-weight: 600;
    font-size: 13px;
    margin-bottom: 10px;
    text-transform: capitalize;
}

.promo-description p {
    font-size: 13px;
    padding-bottom: 0.5em;
}

.promo-description ul {
    padding: 0.5em;
    margin-bottom: 1em;
    list-style-type: decimal;
    list-style-position: outside;
    padding-left: 2em;
}

.promo-description ul li {
    padding-bottom: 0.5em;
    line-height: 13px;
    font-size: 11px;
}

.play-promo-btn a {
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #24ac7b;
    color: #ffffff;
    font-size: 13px;
    padding: 14px 10px;
    border-radius: 2px;
    font-weight: 600;
    text-transform: uppercase;
}

.product-promotion.disabled-promo {
    pointer-events: none;
    cursor: default;
    filter: gray; /* IE6-9 */
    -webkit-filter: grayscale(1); /* Google Chrome, Safari 6+ & Opera 15+ */
    filter: grayscale(1); /* Microsoft Edge and Firefox 35+ */
}

.bonus {
    background-color: var(--bg-markets);
    border-radius: 5px;
    padding: 1em;
    color: #ffffff;
    font-size: 13px;
    position: relative;
    width: 100%;
    height: 110px;
    border: 1px solid #25292e;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.bonus a {
    color: var(--color-active);
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
}

.disabled-bonus {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: right;
    align-content: center;
    height: 100%;

    background-color: #000;
    background-color: rgba(24, 26, 28, 0.7);
    border-radius: 5px;
}

.bonus-icon .bi {
    color: var(--bg-active);
    font-size: 30px;
    padding-right: 5px;
    opacity: 0.5;
}

.bonus span {
    display: flex;
    flex-direction: column;
}

.bonus span.bonus-amount {
    padding: 0.5em 0;
    font-size: 24px;
    font-weight: 900;
    letter-spacing: 0.5px;
}

.bonus-text {
    font-size: 13px;
    font-weight: 600;
}

.bonuses-wrapper .row .col-4 {
    display: flex;
    align-items: stretch;
}

.bonuses-wrapper .row .col-12 {
    margin-bottom: 1em;
}

.bonus-details-wrap {
    width: 100%;
}
.bonus-details {
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.bonus-details .bonus-text {
    font-size: 1em;
}

.bonus-details .bonus-amount {
    font-size: 3em;
    padding: 0.3em 0;
    font-weight: 900;
    color: #24ac7b;
}

.bonus-details p {
    color: #fff;
}

.disclaimer p {
    color: #fff;
    font-size: 11px;
    margin-bottom: 0;
    padding: 1em 0;
}

#instant-games {
    width: 100%;
    background-color: #2d001f;
    min-height: 95vh;
}

#instant-games .card-header {
    background-color: #27273f;
}

#instant-games .card-header-tabs li.nav-item {
    display: flex;
    flex-direction: row;
    width: 33.333%;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    border-bottom: none;
}

#instant-games .tab-content {
    color: #ffffff;
}

#instant-games .tab-content > .tab-pane p {
    font-size: 13px;
    line-height: 20px;
}

#instant-games .tab-content > .tab-pane h5 {
    font-size: 13px;
}

#instant-games .nav-tabs .nav-link.active {
    background-color: transparent !important;
    color: var(--color-active) !important;
    width: 100%;
}

#instant-games .nav-tabs a.nav-link {
    width: 100%;
}

.instant-games-balance {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.instant-games-balance p {
    color: #f3c717;
    font-size: 12px;
    padding-bottom: 0.5em;
    padding-top: 1em;
}

.instant-games-balance h5 {
    font-size: 13px;
    font-weight: 600;
}

.instant-balance {
    width: 50%;
    text-align: right;
    border-right: 1px solid #f3c717;
    padding-right: 3%;
}

.instant-bonus {
    width: 48%;
}

.instant-games-wrapper {
    background-color: #180010;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    flex-wrap: wrap;
    padding: 10px;
}

.instant-game {
    width: 31.333%;
    margin: 1%;
    border-radius: 5px;
    border: 2px solid #f3c717;
    max-width: 165px;
    cursor: pointer;
}

.instant-game img {
    width: 100%;
    max-width: 160px;
}

.instant-selected {
    width: 31.333%;
    margin: 1%;
    border-radius: 5px;
    border: 2px solid var(--color-active);
    max-width: 165px;
    cursor: pointer;
}

.instant-selected img {
    width: 100%;
    max-width: 160px;
}

.treasure-box img {
    border-radius: 5px;
    max-width: 160px;
}

.instant-deposit {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    flex-wrap: wrap;
    padding: 0 15px;
}

.instant-deposit h5 {
    width: 100%;
    margin-bottom: 1em !important;
    margin-top: 1em;
}

.instant-deposit h5.small {
    font-size: 100%;
}

.instant-deposit-input,
.instant-deposit-submit,
.instant-deposit-btns {
    width: 100%;
}

.instant-deposit-input .form-control {
    border: 2px solid #f3c717;
}

.instant-deposit-input input.form-control {
    background-color: #2d001f !important;
    height: calc(1.5em + 1.5rem + 2px);
}

.instant-deposit-btns .btn {
    border: 2px solid #f3c717;
    color: #ffffff;
    padding: 0.9rem 1rem;
}

.deposit-instant-btns {
    background-color: #f3c717;
    color: #292929 !important;
}

.instant-deposit-btns .form-control {
    border: 2px solid #f3c717;
}

.instant-deposit-btns {
    display: flex;
    align-items: center;
    justify-content: center;
}

.instant-amount {
    width: 100%;
    padding-inline: 2px;
}

.instant-deposit-submit .btn {
    background-color: #f3c717;
    font-weight: 600;
    font-size: 1.2rem;
}

.instant-game-result {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    flex-direction: column;
    position: relative;
}

.instant-game-result img {
    width: 100%;
}

.treasure-box-outcome {
    width: 100%;
    position: relative;
}

.treasure-box-outcome-message {
    position: absolute;
    width: 90%;
    top: 5%;
    margin: auto;
    text-align: center;
}

.treasure-box-outcome-message h5 {
    color: var(--color-active);
    font-size: 2em !important;
    text-transform: capitalize;
    font-weight: 700;
}

.treasure-box-total-outcome {
    position: absolute;
    width: 90%;
    bottom: 8%;
    margin: auto;
    text-align: center;
}

.treasure-box-total-outcome h5 {
    color: var(--color-active);
    font-size: 1.5em !important;
    text-transform: capitalize;
    font-weight: 700;
}

.treasure-box-total-outcome h5 span {
    color: #ffffff;
}

.treasure-box-outcome-prize {
    position: absolute;
    width: 90%;
    text-align: center;
}

.treasure-box-outcome-prize h5 {
    color: #ffffff;
    font-size: 4em !important;
    text-transform: capitalize;
    font-weight: 700;
}

.play-again-btn {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    flex-wrap: wrap;
    padding: 15px;
}

.double-chance-market {
    white-space: nowrap;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
}

.double-chance-market.select-other-markets {
    width: 100%;
}

.playing-games-wrapper {
    width: 100% !important;
    padding-bottom: 0;
}

.od-wrap {
    width: 100%;
    background-color: var(--bg-market-wrap-odd);
}

.even-wrap {
    width: 100%;
    background-color: var(--bg-market-wrap-even);
}

.team-badge {
    padding-right: 0;
    width: 20px !important;
}

.team-badge img {
    width: 17px;
    display: inline-block;
    margin: 2px 0;
}

.game-time-and-day {
    font-size: 13px;
}

.stake-and-possible-win {
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.see-final-outcome {
    padding: 2px 0;
    font-size: 14px;
    color: var(--color-betslip-market);
}

.see-final-outcome-stake{
    color: #FFF;
    font-size: 14px;
}

.playing-teams span.team-name-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    font-size: 15px;
    padding-left: 3px;
}

.match-standing {
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    font-size: 1.13em;
}

.standing-team-position {
    padding-right: 0.3em;
}

.match-standing-wrapper table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    margin: 0 auto 1em;
}

table thead {
    color: var(--bg-markets);
    font-weight: 400;
    text-align: left;
    background: transparent;
    font-size: 1.13em;
    display: flex;
    vertical-align: bottom;
    width: 100%;
}

table thead tr th:nth-child(1) {
    padding: 4px !important;
    text-align: center;
    width: 50px;
}

table thead tr th:nth-child(2) {
    width: 100%;
    padding-left: 5px;
    display: flex;
    align-items: center;
}

table thead tr th:nth-child(3) {
    text-align: center;
    padding: 10px 5px !important;
    width: 55px;
}

table thead tr th:nth-child(4) {
    width: 170px;
    text-align: center;
}

td,
th {
    border: 2px solid #343a40;
}

tr td:nth-child(1) {
    text-align: center;
    padding: 4px;
    width: 40px;
    font-size: 15px;
}

table tr td:nth-child(2) {
    width: 100%;
    padding-left: 5px;
}

tr td:nth-child(3) {
    text-align: center;
    font-size: 1.13em;
    font-style: normal;
    font-weight: 400;
    line-height: 13px;
    letter-spacing: 0em;
    padding: 4px !important;
    width: 50px;
}

tr td:nth-child(4) {
    width: 115px;
    text-align: center;
}

.team-form {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
}

tr:nth-child(even) {
    background: transparent;
}

tr {
    width: 100%;
    vertical-align: bottom;
    border-color: inherit;
}

.ods-wrap {
    display: flex;
    align-items: center;
}

.reemove-this-team a .bi {
    font-size: 2em;
    color: #fff;
}

.stake-input input::placeholder {
    color: var(--bg-markets);
    font-size: 1em;
}

.stake-input ::-webkit-input-placeholder {
    color: var(--bg-markets);
}
.stake-input ::-moz-placeholder {
    color: var(--bg-markets);
}
.stake-input :-ms-input-placeholder {
    color: var(--bg-markets);
}
.stake-input :-moz-placeholder {
    color: var(--bg-markets);
}

.total-payout, .total-payout-amount {
    font-weight: 600;
    font-size: 17px;
}

.betslip-active {
    width: 100%;
    display: flex;
}

.deactivated-game {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    text-transform: capitalize;
    text-align: center;
    width: 100%;
    font-size: 0.9em;
    background-color: #ffcdd2;
    color: #b71c1c;
    padding: 0.2em 1em;
    margin-top: 3px;
    font-weight: 300;
}

.app-bet-time li.next-game-time.active {
    color: var(--color-active);
    border-bottom: var(--color-active) solid 3px;
    font-weight: 500;
    flex-grow: 0;
}

.live {
    background-color: red;
}

/* -------------- Start Of Prevent scrollbars to appear when waves go out of bound --------------*/
.sonar-wrapper {
    position: relative;
    z-index: 10;
    overflow: hidden;
    padding: 5rem 0;
}

.sonar-emitter {
    position: relative;
    margin: 0 auto;
    width: 90px;
    height: 90px;
    border-radius: 9999px;
    background-color: var(--color-active);
}

.sonar-wave {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 9999px;
    background-color: rgba(0, 255, 180, 1);
    opacity: 0;
    z-index: -1;
    pointer-events: none;
}

.sonar-wave {
    animation: sonarWave 2s linear infinite;
}

@keyframes sonarWave {
    from {
        opacity: 0.4;
    }
    to {
        transform: scale(3);
        opacity: 0;
    }
}

.time-count-down {
    position: absolute;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90px;
    height: 90px;
    font-size: 3em;
    font-weight: 600;
}

.time-counter-heading {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    width: 100%;
    height: 50px;
    font-size: 2em;
    font-weight: 600;
    color: var(--color-active);
}

/*-------------- End of Prevent scrollbars to appear when waves go out of bound  --------------*/

/* --------------Start of Other Markets--------------------------- */

.event {
    margin-bottom: 1em;
}

.event-t {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5em;
}

.btn-odd-option .odd-selection {
    flex: 1 0 50px !important;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.event-t .team-badge {
    display: flex;
    align-items: center;
    color: var(--color-betslip);
    width: auto !important;
}

.event-t .team-badge span {
    padding-inline: 0.4em;
}

.divider {
    color: #fff;
    padding-inline: 1em;
}

.event-market .btn-odd-option {
    padding-right: 0;
}

.game-odds {
    font-size: 1.13em;
    color: var(--bg-active);
    text-align: center;
}

.betslip .col-8 {
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
}

.card-body h2 {
    font-size: 1.4em !important;
}

.search-bar button {
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}

.search-bar .form-control {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-right: none;
}

.money-box-wrapper {
    background: #1b0013;
}

.money-box-wrapper ul.nav{
    background: var(--color-accent) !important;
}

.money-box-wrapper ul{
    background: var(--color-accent) !important;
}

.money-box {
    padding-top: 1em;
    padding-right: 15px;
    padding-left: 15px;
    color: #fff;
}

.money-box #deposit {
    padding-left: 5px;
    padding-right: 5px;
}

.money-box-wallet-balance {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;
}

.money-box-wallet-bonus {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
}

.money-box-wrapper #bet-slip a{
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: center;
    color: #fff;
    font-size: 12px;
}

.money-box-wrapper #bet-slip a img{
    max-width: 25px;
    margin: 0 2px;
    width: 25px;
}

.wallet-number {
    color: #fff;
    font-size: 1.5em;
    line-height: 1.5em;
    font-weight: 800;
}

.wallet-text {
    color: #f3c717;
}

.money-box-wrapper .nav-tabs .nav-item button.active {
    color: var(--color-active);
}

.money-box-wrapper .nav-tabs .nav-item {
    display: flex;
    flex: 1 0 70px;
    text-align: center;
    justify-content: center;
    align-items: center;
}

.money-box-wrapper .nav-tabs .nav-item button {
    width: 100%;
    border-radius: 0;
    background-color: #16202c;
    background: var(--color-accent) !important;
    text-transform: capitalize;
    font-size: 1em;
    font-weight: 400;
    padding: 0.5rem 0.5rem;
}

.money-box-image-wrap {
    width: 33.333%;
    padding: 2px;
}

.money-box-image {
    position: relative;
}

.money-box-odds {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 5px;
    width: 100%;
}
.odds-directions {
    padding-left: 2px;
}

.odds-directions .bi {
    font-size: 13px;
}

.odds-directions .bi-caret-up-fill {
    color: green;
}

.odds-directions .bi-caret-down-fill {
    color: red;
}

.money-box .col-12 {
    display: flex;
    flex: 1 1;
    flex-wrap: wrap;
    margin: 0;
    padding: 4px;
}

.other-money-boxes-heading {
    /* background-color: #2d001f; */
    width: 100%;
    text-align: center;
    padding: 4px;
    color: #fff;
    font-size: 14px;
    text-transform: capitalize;
}

.other-money-boxes {
    display: flex;
    padding: 5px;
    flex-wrap: wrap;
    margin-right: 0;
    margin-left: 0;
}

.other-money-boxes .money-box-image-wrap {
    width: 16.666666667%;
}

.deposit-btn-wrap{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
}

.deposit-btn {
    display: flex;
    flex-wrap: wrap;
    gap: 5px 5px;
    width: 100%;
}

.money-box #deposit .deposit-btn input[type="submit"]{
    margin-bottom: 0;
    padding: 0.5rem 0.75rem !important;
}

.deposit-btn input[type="submit"] {
    width: 100%;
    margin-bottom: 10px;
    margin-inline: 3px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1 1;
    font-size: 1.3em;
}

#deposit .submit-btn{
    margin-top: 10px;
}

#deposit {
    width: 100%;
    padding-left: 15px;
    padding-right: 15px;
}

#deposit h2 {
    font-size: 1.2em !important;
}

.submit-deposit {
    align-items: center;
    background-clip: padding-box;
    background: #f3c717;
    border: 1px solid #990808;
    background: -moz-linear-gradient(92deg, #990808 0%, #f97c00 100%);
    background: -webkit-linear-gradient(92deg, #990808 0%, #f97c00 100%);
    background: -ms-linear-gradient(92deg, #990808 0%, #f97c00 100%);
    border: none;
    color: #ffffff !important;
    padding: 5px 10px;
    line-height: 30px;
    -moz-border-radius: 30px;
    border-radius: 5px;
    font-size: 1.3em;
    width: 100%;
    text-transform: uppercase;
    font-weight: 900;
    cursor: pointer;
}

#deposit input[type="button"],
#deposit input[type="submit"],
#deposit input[type="type"],
#deposit input[type="reset"] {
    color: #495057;
    background-color: transparent !important;
    background-clip: padding-box;
    border: 2px solid #f97c00;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    padding: 15px 15px;
    line-height: 10px;
    width: 100%;
}

#deposit input[type="type"] {
    font-size: 1.3em;
    padding: 10px 15px;
}

.money-box-image-wrap.money-box-won {
    width: 100%;
}

.money-box-won .money-box-odds {
    font-size: 4em;
    font-weight: 700;
    display: flex;
    top: 0;
    bottom: 0;
    color: #fff800;
}

.congratulations-text {
    /* position: absolute; */
    top: 20px;
    width: 100%;
    left: 0;
    right: 0;
    text-align: center;
    color: var(--color-active);
    font-size: 2em;
    text-transform: capitalize;
}

.confirmation-text {
    /* position: absolute; */
    width: 100%;
    left: 0;
    right: 0;
    text-transform: capitalize;
    text-align: center;
    color: #ffffff;
    bottom: 20px;
    font-size: 1.5em;
}

.confirmation-text span.money-box-amount-won {
    color: var(--color-active);
    font-size: 1em;
}

.other-money-boxes .confirmation-text {
    bottom: 2px;
    font-size: 1em;
    color: var(--color-active);
}
.other-money-boxes .congratulations-text {
    top: 5px;
    font-size: 1em;
    color: #ffffff;
}

.league-free-game .single-match {
    padding: 20px 0;
}

.league-free-game .free-bets {
    padding-bottom: 0;
}

.league-free-game .free-game-content a {
    width: 100%;
}

.team-form .btn-sm,
.btn-group-sm > .btn {
    padding: 0 !important;
    width: 1.5em;
    display: flex;
    align-items: center;
    justify-content: center;
}

#confetti-canvas {
    position: absolute;
    /* bottom: 0 !important; */
    /* max-width: 540px; */
    width: 100%;
    margin: auto;
    left: 0;
    right: 0;
}
.account-balance {
    font-size: 28px !important;
    font-weight: 700 !important;
}

.account-balance .currency-type {
    font-size: 28px !important;
    font-weight: 700 !important;
}

/* --------------share-happiness---------------------- */

.faqs-wrapper .card-header {
    padding-inline: 0;
}

.faqs-wrapper .card-header button.btn {
    padding-inline: 0 !important;
}

.important-info .faqs-wrapper .card-body {
    opacity: 1;
}

.important-info .faqs-wrapper .card-body ul {
    list-style: decimal;
    padding: 10px;
}

.important-info .faqs-wrapper .card-body ul li {
    margin-bottom: 0.5em;
}

.share-happiness .bi {
    font-size: 2em;
    color: var(--color-active);
    padding: 5px;
}

.share-happiness span {
    text-transform: capitalize;
}

.faqs-tab-section p {
    padding: 0.5rem 0.75rem !important;
}

.referral-form-wrap .card .card-body {
    padding-bottom: 0 !important;
}

.match-standing-wrapper table tr.table-header {
    height: 20px;
    background: #f7f7f7;
}

.match-standing-wrapper table tr.table-header th {
    padding-top: 5px;
    padding-bottom: 4px;
    border: 1px solid #f1f1f1;
}

.match-standing-wrapper table tr.table-header th:nth-child(1) {
    text-align: center;
    border: 1px solid #f1f1f1;
}
.match-standing-wrapper table tr td:nth-child(1) {
    width: 2.5em;
    border: 1px solid #f1f1f1;
}

.match-standing-wrapper table tr.table-header th:nth-child(2) {
    padding-left: 5px;
}
.match-standing-wrapper table tr td:nth-child(2) {
    padding: 5px;
    border-bottom: 1px solid #f1f1f1;
}

.match-standing-wrapper table tr.table-header th:nth-child(3) {
    text-align: center;
}

.match-standing-wrapper table tr td:nth-child(3) {
    width: 2.5em;
    border: 1px solid #f1f1f1;
}
.match-standing-wrapper table tr.table-header th:nth-child(4) {
    padding-left: 5px;
}

.match-standing-wrapper table tr td:nth-child(4) {
    width: 95px;
    border: 1px solid #f1f1f1;
}

.subscriptions.card .card-body {
    padding-left: 0;
    padding-right: 0;
    border-bottom: 1px solid #343a40;
    padding-bottom: 1em !important;
}

.b2b-transaction-wrap .subscriptions .card-body {
    padding-bottom: 0.5em !important;
}

.group-teams .card-body {
    padding: 0;
    float: left;
    width: 100%;
}

.group-teams .card-header {
    padding: 0.1rem 1.25rem;
    background-color: var(--color-primary);
}

.group-teams .card-header h5 button.btn {
    padding: 0.1rem !important;
    width: 100%;
    text-align: left;
    font-size: 1em;
}

.group-teams .card {
    margin-bottom: 0px;
}

.display-banner {
    display: block !important;
}

/* --------------tournament---------------------------- */
#tournament .timer {
    background-color: #820849;
    padding: 10px 5px;
}
#tournament .select.active-select {
    background-color: var(--bg-active);
}

#tournament .select.active-select select{
    color: var(--color-secondary);
}

#tournament .btn.btn-warning {
    background-color: #48bbf4 !important;
}
#tournament .odd-selection.added-game-btn {
    background-color: var(--bg-active);
}
#tournament a.place-bet {
    background-color: #48bbf4;
}

#tournament .my-slip-teams {
    border: 1px solid #48bbf4;
    background-color: #48bbf4 !important;
}
#tournament .account-balance-wrap a {
    background-color: var(--color-active);
    border: 2px solid var(--color-active) !important;
    color: var(--color-active);
}

#tournament .group-teams .card-header {
    background-color: #820849;
    border-radius: 0;
}

#tournament .group-teams .card-header button {
    width: 100%;
}

#tournament .group-teams .card-header button.btn {
    padding: 0.35rem 0 !important;
    font-size: 1em;
    text-transform: capitalize;
}

#tournament .standing-wrapper {
    background-color: #48bbf4;
    border-bottom: 1px solid #48bbf4;
}

#tournament .group-stage {
    display: flex;
    justify-content: space-between;
}

#tournament .time-playing {
    color: var(--bg-active);
    padding-left: 5px;
}

#tournament .match-playing-time {
    display: flex;
    justify-content: space-between;
    width: 100%;
    color: var(--bg-active);
    opacity: 1;
    font-size: 1em;
    font-family: "Roboto Condensed", sans-serif;
    font-weight: 400;
    padding-top: 1px;
}

#tournament .score-result {
    color: #fff;
    font-size: 0.85em;
    font-family: "Roboto Condensed", sans-serif;
    font-weight: 400;
    display: flex;
    align-items: center;
}

#tournament .score-result-icon img {
    width: 12px;
    margin-right: 5px;
}

#tournament .league-wrapper {
    min-height: auto;
    padding-bottom: 0;
    background-color: white;
}

#tournament .account-balance-wrap .login-nav a {
    color: #FFF;
}

.games-selection {
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.games-selection a {
    color: #fff;
    padding: 10px 0;
}

.games-selection.active-tab a {
    color: var(--bg-active);
}
.winning-combination {
    padding: 1em 0;
    float: left;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    text-transform: capitalize;
    font-weight: 400;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.group-teams button.btn {
    width: 100%;
    padding: 0.1rem 0.5rem !important;
}
.group-teams .group-stage {
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    font-size: 16px;
    text-transform: capitalize;
}

#tournament .jackpot-results-outcome .group-teams .card-header {
    padding: 0.1rem 0;
}

#tournament .match-standing-wrapper td.playing-teams {
    justify-content: flex-start;
}

#tournament .playing-teams {
    justify-content: space-between;
    padding-bottom: 3px;
}

#tournament .half-time-score {
    padding-right: 5px;
    opacity: 0.4;
}

#tournament .half-and-full-time-score {
    display: flex;
    justify-content: flex-end;
}

#tournament .half-time {
    padding-right: 3px;
}

#tournament .match-standing-wrapper table tr td:nth-child(1) {
    width: 30px;
}

#tournament .match-standing-wrapper table tr td:nth-child(3) {
    width: 2.5em;
}
#tournament .match-standing-wrapper table tr td:nth-child(4) {
    width: 2.5em;
}
#tournament .match-standing-wrapper table tr td:nth-child(5) {
    width: 2.5em;
    text-align: center;
    border: 1px solid #f1f1f1;
}

#tournament .match-standing-wrapper table tr td:nth-child(6) {
    width: 2.5em;
    border: 1px solid #f1f1f1;
    text-align: center;
}

#tournament .match-standing-wrapper table tr td:nth-child(7) {
    width: 2.5em;
    text-align: center;
    border: 1px solid #f1f1f1;
}

#tournament .match-standing-wrapper table tr td:nth-child(8) {
    width: 2.5em;
    text-align: center;
    border: 1px solid #f1f1f1;
}

#tournament .match-standing-wrapper table tr.table-header th:nth-child(1),
#tournament .match-standing-wrapper table tr.table-header th:nth-child(3),
#tournament .match-standing-wrapper table tr.table-header th:nth-child(4),
#tournament .match-standing-wrapper table tr.table-header th:nth-child(5),
#tournament .match-standing-wrapper table tr.table-header th:nth-child(6),
#tournament .match-standing-wrapper table tr.table-header th:nth-child(7),
#tournament .match-standing-wrapper table tr.table-header th:nth-child(8) {
    text-align: center;
}

#tournament .match-standing-wrapper table {
    margin-bottom: 0;
}

#tournament .match-standing-wrapper {
    margin-bottom: 0;
    background-color: #ffffff;
    float: left;
    width: 100%;
}

#tournament .match-standing-wrapper h6 {
    font-size: 14px;
    padding: 5px 10px;
}

#tournament .standing-heading,
#tournament .standing-time {
    color: var(--color-secondary);
    text-transform: capitalize;
}

.games-selection-tab{
    background: var(--color-accent) !important;
}

#tournament .games-selection.active-tab a{
    font-size: 14px;
}

.affiliate-tab {
    margin-top: 1em;
    background-color: var(--color-accent);
    border-radius: 0.25rem;
}

.affiliate-tab .card {
    padding-top: 10px;
    padding-bottom: 10px;
    margin-bottom: 2px;
    padding-inline: 10px;
    border-radius: 0;
    border-bottom: 1px solid var(--color-primary);
}

.affiliate-tab .card:last-child{
    border-bottom: 0;
}

.affiliate-tab .card .card-body {
    padding: 0;
}

.affiliate {
    display: flex;
    flex-direction: row;
    font-size: 1em;
    align-items: center;
}

.affiliate span {
    padding-right: 5px;
    text-transform: capitalize;
    margin: 2px 0;
    font-size: 13px;
    color: #fff;
}

.affiliate span:first-child {
    width: 100px;
}

.affiliate span.referral-numbers span:first-child {
    width: auto;
}

.affiliate span.referral-numbers span {
    padding-inline: 10px;
}

.affiliate span.stake-amount span:first-child {
    width: auto;
}

.affiliate span.stake-amount span {
    padding-inline: 10px;
}

.moss-play-wrapper {
    width: 100%;
    background-color: #f9f9f9;
}

.moss-play-menu-icon{
    display: flex;
    justify-content: flex-start;
}

.moss-play-menu-icon img {
    max-width: 40px;
    width: 40px;
    padding-left: 10px;
}

.moss-play-wrapper .user-image {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.moss-play-wrapper .user-image img {
    width: 50px;
    margin: auto;
}

.moss-play-games .col-6 {
    padding-inline: 4px !important;
    margin-bottom: 10px;
}

.moss-game-wrap {
    position: relative;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    border-radius: 7px;
}

.tag{ position: absolute;
    top: 0;
    left: 0;
    background-image: url('https://storage.googleapis.com/kironlite/images/lite-tag.png');
    background-size: cover;
    background-position: top left;
    background-repeat: no-repeat;
    z-index: 10;
    width:75px;
    height: 70px;
}

.moss-game-wrap img {
    border-radius: 7px;
}

.moss-game {
    position: absolute;
    padding: 1em;
    width: 100%;
    bottom: 0;
    height: 0%;
    /* background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5)); */
}

.moss-game-cta {
    position: absolute;
    bottom: 3px;
    display: flex;
    flex-direction: row;
    justify-content: right;
    width: 100%;
    left: 0;
    right: 0;
}

.moss-game-cta a {
    margin-right: 4px;
    font-size: 1em;
}

.moss-play-wrapper .currency-type,
.moss-play-wrapper .account-balance {
    font-size: 16px !important;
}

.b2b-transaction-wrap .subscription-status.credit-status{
    background: rgba(81, 164, 86, 1);
    background: #28a745 !important;
    font-size: 12px;
    font-weight: 700;
    padding: 5px 8px;
}

.b2b-transaction-wrap .subscription-status.debit-status{
    background: rgba(245, 194, 68, 1);
    background: #dc3545 !important;
    font-size: 13px;
    font-weight: 700;
    padding: 5px 8px;
}

.footer {
    background-color: #f9f9f9;
}

.footer .play-responsibly{
    text-align: center;
    width: 100%;
}

.footer .col-12 {
    display: flex;
    align-items: center;
    width: 100%;
}

.footer-logo img {
    max-width: 120px;
    margin-bottom: 0 !important;
}

.play-responsibly img {
    width: 120px;
}

.social-media {
    display: flex;
    flex-direction: row;
    width: 100%;
}

.social-media a {
    padding-inline: 3px;
    font-size: 20px;
    color: var(--color-active);
}

.social-media a.bi {
    color: var(--color-active);
}

.moss-game-cta a.badge {
    padding: 0.75em 0.75em;
}

.moss-play-wrapper .card-body {
    display: flex;
    flex-direction: row;
    width: 100%;
}

.moss-play-wrapper .card-body .user-image {
    padding-right: 10px;
}

.moss-play-wrapper .card-body .wallet-wrap {
    text-align: left;
    width: 100%;
    border-left: 1px solid #ccc;
    padding-left: 10px;
}

.account-access.account-balance-wrap.moss-play-account a {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 13px;
    color: var(--color-active);
}

.account-user-name {padding-left: 2px;}

.sidenav {
    height: 100%;
    width: 0;
    position: absolute;
    z-index: 1000;
    top: 0;
    left: 0;
    background-color: var(--color-secondary);
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 65px;
}

.sidenav a {
    padding: 10px 10px 10px 20px;
    text-decoration: none;
    font-size: 15px;
    color: #fff;
    display: block;
    transition: 0.3s;
    border-bottom: 1px solid var(--bg-markets);
}

.sidenav a:hover {
    color: #f1f1f1;
}

.sidenav .closebtn {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 21px;
    padding: 20px 20px;
    width: 100%;
    text-align: right;
    border-bottom: 1px solid var(--bg-markets);
    cursor: pointer;
}

.sidenav .bi {
    opacity: 0.6;
}

.sidenav a span {
    padding-right: 10px;
}

.closebtn {
    color: #f1f1f1;
}

.share-bet-stake-and-wrap a.win-amount{
    color: #FFF;
    font-size: 16px;
}

.loadmore-field{
    width: 100%;
    float: left;
}

.loadmore-field button{
    background-color: var(--bg-markets);
    border-color: var(--bg-markets);
    color: #FFF;
    border-radius: 3px;
}

.modal-body{
    padding: 0 0;
}
.modal-body img{
    margin-bottom: 1em;
}

.week-round-content{
    font-size: 16px;
}

.other-box-container {
    display: flex;
    flex-wrap: wrap;
    gap: 5px 5px;
    width: 100%;
}

.other-box{
    position: relative;
    font-size: 13px;
    flex: 1 0 50px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #ffff;
}

.other-box-odds{
    position: absolute;
    bottom: 2px;
}

.other-box .congratulations-text{
    font-size: 12px;
    top: 2px;
}

.won-boxes-container{
    display: flex;
    flex-wrap: wrap;
    gap: 5px 5px;
    width: 100%;
}

.won-boxes-container .other-box{
    flex: 1 0 100px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.money-box-not-won{
    position: relative;;
}

.money-box-not-won .confirmation-text{
    position: absolute;
    display: flex;
    justify-content: center;
    text-align: center;
    width: 100%;
}

.money-box-not-won .confirmation-text .other-box-odds{
    font-size: 30px;
    color: var(--color-active);
}

.other-markets-wrapper .btn-wrapper{
    display: flex;
    flex-wrap: wrap;
    gap: 1px;
    width: 100%;
}

.league-games-matches{
    width: 100%;
}

.league-anthem{
    opacity: 0;
}

a.card-tag {
    display: block;
}

#tournament #app{
    background: var(--color-secondary);
}
.dropdown-select {
    width: 125px;
}

.dropdown-select {
    min-width: 125px;
}
.double-chance-market.select-other-markets .select{
    min-width:110px;
    font-size: 13px;
}
.sport_dropdowns button {
    font-size: 14px;
}

.b2b-transaction-wrap{
    background-color: var(--bg-markets);
    border-radius: 3px;
}

.b2b-transaction-wrap .wallet-balance .card{
    padding: 0 !important;
    padding-top: 5px !important;
}

.subscriptions{
    padding-left: 5px;
    padding-right: 5px;
}

.account-balance .currency-type{
    text-transform: uppercase;
}

.mb-3.time-payment-was-done{
    margin-bottom: 10px !important;
}

.tab-details.my-betslip .icon{
    position: relative;
}

.bet-counter{
    position: absolute;
    border: 1px solid var(--bg-active);
    background-color: var(--bg-active) !important;
    color: var(--bg-markets);
    width: 18px;
    height: 18px;
    border-radius: 50%;
    padding: 2px;
    font-size: 14px;
    line-height: 12px;
    font-weight: 900;
    left: 5px;
    top: -10px;
}

.my-bets-container{
    background-color: var(--bg-my-container);
    border-radius: 5px;
    padding: 0.70em !important;
}

.my-bets-container button.btn{
    font-size: 18px;
}


.odds-container{
    background-color: var(--bg-odds-container);
    border-radius: 5px;
}

.odds-container span{
    font-size: 18px;
}


.logout-screen{
    background-color: #212335;
    background: var(--color-secondary);
    --color-secondary: #0a1f2f;
    width: 100%;
    height: 100vh;
    background-image: url('../images/pattern.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 300px;
    padding-inline: 10px;
}


.logout-screen .form-content{
    height: 90vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.logout-screen .form-content h2{
    font-size: 2.8em !important;
}

.logout-screen .form-content h6{
   font-size: 1.7em !important; 
}

/* ---------------Media Queries---------------------------- */

@media handheld, only screen and (max-width: 768px) {
    body{
        background-image: none;
    }
}


@media handheld, only screen and (max-width: 480px) {
    .page-nav a {
        padding: 10px 15px;
        font-size: 12px;
    }

    .moss-play .logo-icon img {
        margin-left: 0;
    }

    .logo-icon img {
        max-width: 100px;
    }
    .home-icon {
        text-align: right;
    }
    .games-nav-wrapper .page-navigation a {
        padding: 8px 5px;
        font-size: 16px;
    }
    .btn-sm,
    .btn-group-sm > .btn {
        padding: 0.1rem 0.2rem;
    }

    .join-logo img {
        max-width: 130px;
    }

    .play-responsibly {
        padding: 10px 0;
    }

    .page-heading{
        font-size: 1.2em;
    }

    .subscribe-wrapper.pt-3.pb-5.px-3{
        padding: 5px !important;
        padding-right: 15px !important;
        padding-left: 15px !important;
    }

    .odds-container span,  .my-bets-container button.btn{
        font-size: 16px;
    }
}

@media screen and (max-height: 460px) {
    .sidenav {
        padding-top: 15px;
    }
    .sidenav a {
        font-size: 18px;
    }
}

@media handheld, only screen and (max-width: 420px) {
    .logo-section {
        height: 2.5em;
    }
    .page-nav a {
        padding: 10px 10px;
        font-size: 11px;
    }
    .account-access.account-balance-wrap a {
        margin-top: 0;
    }

    .referral-form-wrap .nav-tabs a.nav-link {
        font-size: 11px;
    }

    .account-successful-created {
        padding: 1em;
    }

    .bonus span.bonus-amount {
        font-size: 13px;
    }

    .logo-icon img {
        max-width: 100px;
    }

    .my-slip-teams {
        width: 45px;
        height: 45px;
    }

    .my-bet-slip {
        width: 56px;
        text-align: left;
        padding-left: 5px;
    }

    .moss-game-cta a.badge {
        font-size: 1em;
    }

    .moss-game-cta a.badge {
        padding: 0.3em 0.3em;
    }

    .odds-container span,  .my-bets-container button.btn{
        font-size: 14px;
    }

    .odds-container{
        padding: 0.3em 0.6em !important;
    }

}

@media handheld, only screen and (max-width: 380px) {
    .page-nav a {
        padding: 10px 10px;
    }
    .account-access a {
        font-size: 1.15em;
    }

    .page-tab-nav {
        flex-wrap: wrap;
    }

    .page-navigation {
        width: 24%;
        margin-bottom: 0.2em;
    }

    .free-game-content {
        top: 0.5em;
    }
    .free-game-content h1 {
        font-size: 1.5em;
    }

    .free-game-content h6 {
        font-size: 1.3em;
    }
    .team-selection-wrapper {
        padding-bottom: 0;
    }
    .menu-icon img {
        width: 35px;
    }

    .games-nav-wrapper .page-navigation a {
        padding: 5px 5px;
        font-size: 13px;
    }

    .welcome-details h2 {
        font-size: 1em !important;
    }

    .account-successful-created h1 {
        padding-bottom: 0.5em;
        font-size: 1.5em;
    }
    .possible-win {
        font-size: 12px;
    }
    .share-bet a {
        padding: 0.5em;
        font-size: 12px;
        height: 40px;
    }

    .btn-sm,
    .btn-group-sm > .btn {
        padding: 0.1rem 0.1rem;
    }

    a.place-bet {
        font-size: 1.3em;
    }

    .my-total {
        font-size: 1em;
    }

    .logo-icon img {
        max-width: 100px;
    }

    .game-type{
        font-size: 11px;
    }

    .footer{
        padding-bottom: 1em !important;
    }

    .moss-play-wrapper .card-body{
        padding-inline: 0;
    }

    .odds-container span, .my-bets-container button.btn{
        font-size: 13px;
    }


}

@media handheld, only screen and (max-width: 360px) {
    .page-navigation {
        width: 23%;
    }

    a.place-bet {
        font-size: 1.1em;
    }

    .my-slip-teams {
        width: 30px;
        height: 30px;
    }
    .tab a {
        font-size: 1em;
    }

    .col-4.account-access {
        margin-left: 0;
    }

    .col-3.account-access.account-balance-wrap.moss-play-account{
        padding-left: 0;
    }

    .card-body h2.wallet-heading{
        font-size: 12px !important;
        margin-bottom: 0 !important;
    }

    .moss-play-wrapper .currency-type, .moss-play-wrapper .account-balance {
        font-size: 13px !important;
    }

    .logo-section a{
        text-align: center;
    }
    #tournament .match-playing-time{
        font-size: 0.85em;
    }
    .double-chance-market.select-other-markets select {
        min-width: 104px;
        font-size: 1em;
    }

    .tab-details.my-bets span{
        font-size: 12px;
    }

}

@media handheld, only screen and (max-width: 320px) {
    .sport_dropdowns button {
        font-size: 9px;
        font-weight: 600;
    }
    .logo-icon img {
        width: 100px;
        display: block;
        margin-left: 15px;
    }

    .logo-icon {
        text-align: left !important;
    }

    .choose-amount-to-deposit .btn {
        padding: 0.3rem 0.2rem !important;
    }

    .choose-amount-to-deposit button span {
        font-size: 12px;
    }

    .referral-form-wrap .nav-tabs a.nav-link {
        font-size: 12px;
    }

    .menu-icon img {
        width: 30px;
    }

    .bonuses-wrapper .row .col-4 {
        max-width: 50%;
        flex: 0 0 50.333333%;
    }

    .time-count-down {
        width: 100px;
        height: 100px;
    }

    .sonar-emitter {
        width: 100px;
        height: 100px;
    }

    .logo-icon img {
        display: none;
        margin-left: -25px !important;
    }

    .logo-icon a {
        background-image: url("https://storage.googleapis.com/kironlite/images/mossbet-1683836074.png");
        background-position: center;
        background-size: 40px;
        background-repeat: no-repeat;
        padding: 25px;
        /* width: calc(((100vw - 100%) / 2) + 10%); */
    }

    #ad-popup-wrapper .close {
        top: 45px;
        right: 25px;
        font-size: 30px;
    }

    .search-game {
        font-size: 12px;
    }

    .menu-wrapper {
        margin-bottom: 0em;
    }

    .menu-wrapper a {
        padding: 0;
    }

    .btn-odd-option .odd-selection {
        width: 100%;
        flex: 1 0 45px !important;
    }

    .modal-header .close .bi {
        font-size: 30px;
    }

    small,
    .small {
        font-size: 100%;
    }

    .form-control-lg {
        height: calc(1em + 1.5rem + 2px);
    }

    .access-area button {
        font-size: 12px;
    }

    .other-money-boxes .money-box-image-wrap {
        width: 33.3333333%;
    }
    .playing-teams{
        width: 100%;
    }

    .page-navigation {
        width: 49%;
    }

    .share-bet-stake-and-wrap a.win-amount {
        font-size: 12px;
    }

    .page-heading{
        font-size: 1em;
    }

    .moss-play-wrapper .card-body{
        padding-inline: 0em;
    }

    .moss-play-wrapper .user-image img {
        width: 60px;
    }

    .moss-play-wrapper .card-body h2 {
        font-size: 1.2em !important;
    }

    .play-responsibly {
        padding: 5px 0;
    }

    .tab-details.my-bets img {
        width: 14px;
    }

}
@media handheld, only screen and (max-width: 320px) {

    .time-payment-was-done .row{
        display: flex;
        flex-direction: column;
    }

    .time-payment-was-done .row .col-6{
        max-width: 100%;
        text-align: center !important;
        font-size: 12px;
    }

    .b2b-transaction-wrap .subscriptions .card-body .row .col-3{
        max-width: 50%;
        flex: 0 0 50%;
        display: flex;
        flex-direction: row;
        text-align: center !important;
        justify-content: center;
        margin-bottom: 4px;
    }

    .b2b-transaction-wrap .currency-type{
        padding-right: 5px;
    }

    .b2b-transaction-wrap .card-body.py-2{
        padding: 0 !important;
    }

    .b2b-transaction-wrap .wallet-balance{
        margin-bottom: 5px !important;
    }

    .account-balance.mb-4{
        margin-bottom: 0 !important;
    }

    .mb-3.time-payment-was-done{
        margin-bottom: 5px !important;
    }

    .playing-games-wrapper {
        padding-bottom: 0;
    }

    .page-header .col-4:nth-child(2) {
        padding-left: 0;
        padding-right: 0;
    }

    .page-header .col-4:nth-child(2) .bi.pl-1{
        padding-left: 0 !important;
    }

    .time-counter-heading{
        font-size: 1.5em;
    }

}
@media handheld, only screen and (max-width: 280px) {
    .bonuses-wrapper {
        padding-top: 0 !important;
        margin-top: 0 !important;
    }
    .games-wrapper .row .col-5 {
        max-width: 38.666667%;
        margin-left: 3px;
    }

    .footer-logo img {
        width: 135px;
    }

    .logo-section {
        height: 3em;
    }

    .logo-icon {
        /* margin-left: -0.5em; */
        text-align: center !important;
    }

    .logo-section .container {
        padding-inline: 5px;
    }
    .container {
        padding-inline: 10px;
    }

    .account-access a {
        font-size: 10px;
    }

    .logo-icon img {
        width: 95px;
        margin-left: 10px;
    }

    .games-nav-wrapper {
        margin-top: 2px;
    }

    .footer {
        padding-top: 2em !important;
    }

    .market-games a {
        margin: 0 5px;
        padding-inline: 0;
    }

    .market-games span.icon-title {
        font-size: 8px;
    }

    .games-nav-wrapper {
        padding: 4px !important;
    }

    .games-wrapper {
        margin-top: -5px;
    }

    .games-wrapper .col {
        padding-left: 0 !important;
        padding-right: 1px !important;
    }

    .games-wrapper .col-7 {
        padding-right: 1px !important;
    }

    .games-wrapper .col-6 {
        margin-top: -0.6em;
    }

    .page-nav span {
        font-size: 8px;
    }

    .other-contacts .bi {
        font-size: 21px;
    }

    .contacts p {
        font-size: 10px;
        padding-inline: 10px;
    }

    .contacts p br {
        display: none;
    }

    .contacts a {
        font-size: 10px;
    }

    .footer-logo a {
        font-size: 13px;
    }

    .sport_dropdowns .col-4 {
        padding-right: 5px;
        padding-left: 5px;
    }

    .sport_dropdowns button.btn {
        padding: 0.5em 1em;
    }

    .select {
        height: 2em;
        line-height: 2;
    }

    .league-display-wrapper {
        font-size: 0.8em;
    }

    .tabcontent {
        padding-top: 0.2em !important;
        padding-bottom: 0.2em !important;
    }

    .playing-teams span {
        width: 100px;
    }

    .single-match .col-8 {
        padding-right: 5px;
        padding-left: 5px;
    }

    .odd-selection {
        min-width: 15px !important;
    }

    .team-selection-wrapper {
        padding-bottom: 0;
    }

    .join-logo img {
        max-width: 100px;
        width: 100px;
    }

    .home-icon {
        margin-top: 0;
    }

    .home-icon .bi {
        font-size: 20px;
    }

    .onboarding .card-body h2 {
        font-size: 0.9em !important;
    }

    .account-successful-created img {
        width: 20px;
    }

    .onboarding .col-12 {
        padding-inline: 0.5em;
    }

    .success-icon .bi {
        font-size: 13px;
    }

    .success-icon-wrap {
        width: 65px;
        height: 65px;
    }

    .success-icon {
        width: 35px;
        height: 35px;
    }

    .form-wrap {
        padding: 1em !important;
    }

    .account-action .btn {
        padding: 0.3rem 0rem;
        font-size: 0.7rem;
    }

    .wallet-amount {
        font-size: 17px !important;
    }

    .select {
        font-size: 0.9em;
    }

    .select::after {
        top: 2px;
    }

    .playing-teams span {
        font-size: 1.15em;
    }

    .account-balance-wrap .login-nav a.deposit-funds,
    .account-access.account-balance-wrap a {
        font-weight: 800;
    }

    .country-flag-icon .icon img {
        width: 20px;
    }

    .country-name {
        font-size: 0.5em;
    }

    .match-number-wrap span {
        font-size: 9px;
    }

    #header {
        height: 3em;
    }

    #other-main-content {
        padding-top: 3em;
    }

    .logo-section .col-6 {
        padding-left: 0;
    }

    .share-bet-stake-and-wrap .col-8 {
        padding-left: 0;
    }

    .share-bet-stake-and-wrap .col-4 {
        padding-right: 7.5px !important;
    }

    .my-bet-slip {
        width: 50px;
        padding-left: 1px;
        font-size: 12px;
    }

    .my-total {
        font-size: 0.9em;
        padding-right: 0;
    }

    .ods-given {
        font-size: 1em;
    }

    .playing-teams span.team-name-text {
        font-size: 15px;
    }

    .league-games-wrapper {
        padding-bottom: 0em;
    }

    .mybet-wrapper {
        font-size: 0.9em;
        margin-top: 1px;
    }

    .game-time-and-day {
        font-size: 0.9em;
    }
    .see-final-outcome {
        font-size: 0.9em;
    }

    .my-bets-wrapper .nav-tabs .nav-item button {
        padding: 0.5rem 0.1rem;
        font-size: 12px;
    }

    .single-bet-outcome {
        margin-bottom: 0 !important;
    }

    .account-access.account-balance-wrap a {
        padding: 10px 5px;
    }

    a.place-bet {
        padding: 10px 0px;
    }

    .stake-input {
        padding: 2px;
    }

    .possible-win {
        height: auto;
    }

    .match-number-wrap .col-6 {
        padding-inline: 3px;
    }

    .timer {
        padding: 10px 5px;
    }

    .logo-icon a {
        background-size: 30px;
    }

    .btn-odd-option .odd-selection {
        min-width: 15px !important;
        width: 100%;
        flex: 1 0 30px !important;
    }

    .match-standing-wrapper .playing-teams {
        font-size: 0.85em;
    }

    .logo-icon a {
        width: 100%;
    }

    .team-form .btn-sm,
    .btn-group-sm > .btn {
        width: 20px;
    }

    table thead tr th:nth-child(1) {
        width: 30px;
    }

    table thead tr th:nth-child(2) {
        width: 64%;
    }

    .match-standing-wrapper table tbody tr {
        overflow-x: scroll;
    }

    .game-action-btn {
        margin-top: -10px;
    }

    .close {
        top: 15px;
        right: 15px;
        font-size: 25px;
    }

    .home-team,
    .away-team {
        max-width: 40px;
        font-size: 0.9em !important;
    }

    .standing-heading {
        font-size: 1em;
    }

    .betslip-wrapper{
        margin-left: 3px;
        margin-right: 3px;
        margin-top: 1px !important;
    }

    .double-chance-market{
        flex: 1 0 40px;
    }

    .select.active-select select{
        font-size: 1em;
    }

    .team-badge img {
        width: 15px;
    }

    .app-bet-time li.next-game-time{
        padding: 0.4em 0.5em;
        font-size: 12px;
    }

    .moss-play-wrapper .user-image img {
        width: 40px;
    }

    .wallet-details{
        flex-direction: column;
        gap: 5px;
    }

    .team-jersey img {
        width: 15px;
    }

    .red-txt, .green-txt {
        font-size: 14px;
    }

    .games-selection.active-tab a, .games-selection a {
        font-size: 12px !important;
    }
    .event-t .team-badge span{
        font-size: 0.9em;
    }

    .playing-teams span{
        max-width: 90px
    }

    .playing-teams span.game-playing {
        font-size: 11px;
        max-width: 100%;
        width: 100%;
        display: flex;
        justify-content: flex-start;
        align-content: center
    }

    .playing-teams span.game-playing span:nth-child(2){
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .betslip .playing-teams .col-9{
        padding-left:2px;
        padding-right:2px;
    }

    .betslip .playing-teams .col-9{
        margin-left: 0px;
    }

    .betslip .playing-teams .col-2{
        margin-left: -20px;
    }

    .playing-teams .col-2 .final-odds-applied{
        font-size: 12px
    }

    .subscribe-wrapper.pt-3.pb-5.px-3{
        padding: 5px !important;
    }

    .moss-play-wrapper{
        padding: 10px !important;
        padding-top: 5px !important;
    }

    .tab-details.my-bets span{
        font-size: 10px;
    }
    .bet-counter{
        width: 14px;
        height: 14px;
        padding: 2px;
        font-size: 12px;
        line-height: 10px;
    }

}


@media handheld, only screen and (max-width: 240px) {
    .playing-teams span.team-name-text {
        font-size: 14px;
    }
    .match-number-wrap .col-7{
        padding-right: 5px;
        padding-left: 5px;
    }
    .match-number-wrap .col-5{
        padding-right: 5px;
        padding-left: 5px;
    }
    .moss-play-menu-icon img{
        width: 30px;
    }
    .account-access.account-balance-wrap a {
        padding: 7px 0px;
        font-size: 0.95em
    }
    .country-flag-icon .icon img {
        width: 15px;
    }
    .tab a {
        font-size: 0.8em;
    }
    .sport_dropdowns button.btn{
        padding: 0.2rem 0.75rem !important;
        font-size: 10px;
    }

    .share-bet-stake-and-wrap .col-4 {
        padding-left: 7.5px !important;
        text-align: center !important;
    }

    .share-bet-stake-and-wrap .col-4:nth-child(2){
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    .moss-play-games .col-6{
        flex: 0 0 100%;
        max-width: 100%;
    }

    #tournament .group-teams .card-header{
        padding: 0.1rem 0.25rem;
    }

    .live-match-selection .col-6{
        padding-left: 10px;
        padding-right: 10px;
    }

    #tournament .group-stage{
        font-size: 12px;
    }

    .games-selection.active-tab a, .games-selection a {
        font-size: 11px !important;
    }

    .match-playing-time{
        font-size: 0.55em !important;
        font-weight: 900 !important;
    }

    .timer{
        font-size: 0.8em;
    }
    .home-team, .away-team{
        font-size: 0.95em !important;
    }

    .home-team .team-badge,
    .away-team .team-badge{
        padding-right: 3px
    }

    .playing-teams span {
        font-size: 16px;
        max-width: max-content;
    }

    .stake-input span.pl-3{
        padding-left: 2px !important;
    }

    .stake-input{
        font-size: 0.8em !important
    }


    .my-bets-container button.btn{
        padding: 0.4rem 0.2rem !important;
        font-size: 11px
    }

    .moss-play-wrapper .currency-type, .moss-play-wrapper .account-balance {
        font-size: 10px !important;
    }

    .wallet-balance .card.p-2{
        padding: 5px !important;
    }

    .wallet-details{
        gap: 3px;
    }

    .wallet-heading{
        font-size: 10px;
        padding: 5px !important;
    }

    .time-payment-was-done{
        font-size: 10px;
    }

    .wallet-balance{
        margin-bottom: 5px !important;
    }

    .market-games img{
        width: 20px;
    }

    .market-games{
        height: 50px;
        padding-top: 10px;
    }

    .sidenav{
        padding-top: 45px;
    }

    .sidenav .closebtn{
        padding: 10px;
    }

    .time-counter-heading{
        align-items: center;
        font-size: 1em;
    }

    .sonar-emitter {
        width: 70px;
        height: 70px;
    }

    .time-count-down {
        width: 70px;
        height: 70px;
        font-size: 2em;
    }

    .sonar-wrapper{
        padding: 4rem 0;
    }

}


@media handheld, only screen and (max-width: 360px) {
    .bg-bonuses{
        flex: 1 0 90px;
    }

    .bg-bonuses span.small{
        font-size: 0.85em;
        color: #33ace2;
    }

    .logout-screen .form-content h2{
        font-size: 2.5em !important;
    }

    .logout-screen .form-content h6{
    font-size: 1.5em !important; 
    }
    
}
@media handheld, only screen and (max-width: 320px){ 

    .logout-screen .form-content h2{
        font-size: 2em !important;
    }

    .logout-screen .form-content h6{
    font-size: 1em !important; 
    }

    .logout-screen{
        background-size: 90%;
    }
}

@media handheld, only screen and (max-width: 240px){ 
    .logout-screen{
        background-size: 300px;
    }
}


@media handheld, only screen and (max-width: 210px){ 
    .bg-bonuses {
        flex: 1 0 100px;
    }
}
