/* ----------------------Rains CSS-------------------------*/
.rains-banner{
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
}

.rains-banner a{
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
}

.rains-banner a img{
    width: 100%;
}

.rains-card-wrapper {
    width: 100%;
    padding: 5px 5px;
    position: absolute;
    bottom: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    max-height: 92vh;
    overflow-y: scroll;
}

#rainsModal .modal-dialog {
    margin: 0 auto;
    max-width: 540px;
    height: 100vh;
}

#rainsModal .modal-content {
    background-color: var(--color-accent);
    background-color: rgba(0, 0, 0, 0.8);
}
.animate-bottom {
    position: relative;
    animation: animatebottom 0.4s;
    height: 100vh;
    margin-top: 0;
}

.rain-card-container {
    background-color: #0B5424;
    padding: 8px 10px;
    border-radius: 10px;
}

.rain-card-hearder {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    padding-bottom: 2px;
    color: #FFF;
    width: 100%;
}

.rain-card-hearder h6 {
    width: 100%;
    text-align: center;
    border: 1px solid #219B4A;
    border-radius: 15px;
    padding: 2px;
    font-size: 14px;
    margin-bottom: 3px;
}

.header-action {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    width: 100%;
}

.rain-card-content {
    width: 100%;
    background-color: #08441D;
    padding: 10px 10px;
    color: #FFF;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.rain-card {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.rain-card a {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    width: 90px;
    text-align: center;
    text-transform: capitalize;
    font-weight: 900;
    white-space: nowrap;
}

.rain-card.claim-now a {
    background-color: #219B4A;
    color: #FFF;
}

.rain-card.claimed a {
    background-color: var(--bg-odd-button-active);
    color: #202020;
}

.rain-card .user-account {
    display: flex;
    align-items: center;
    flex-direction: row;
    gap: 5px;
}

#rainsModal .avatar {
    display: flex;
    width: 20px;
}

#rainsModal .account-number{
    font-size: 13px;
}

.total-rains-footer {
    padding-top: 0;
}

.total-rains-available {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    color: #8C9A9F;
    gap: 10px;
    font-size: 18px;
}

#rainsModal .modal-header {
    position: relative;
    width: 100%;
    bottom: 0px;
}

.rains-text-wrap {
    border-right: 1px solid;
    padding-right: 10px;
}

.rains-value {
    color: #FFF;
}

.rains-text-wrap {
    border-right: 1px solid;
    padding-right: 10px;
}

.rains-icon {
    color: #FFF;
}

#rainsModal .close {
    bottom: 0;
    font-size: 16px;
    width: 100%;
    margin: auto;
    left: 0;
    right: 0;
    background-color: var(--color-active);
    border-radius: 0;
    text-transform: capitalize;
    opacity: 1;
    text-shadow: 0 1px 0 #fff;
    color: #FFF;
}

.notification-text .alert p {
    padding-bottom: 0;
    font-size: 11px;
}

.notification-text .alert button {
   padding: 2px;
}
.notification-text .alert button .bi{
    font-size: 16px;
}
