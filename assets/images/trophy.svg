<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.2.3, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1"
	 id="Layer_1" image-rendering="optimizeQuality" shape-rendering="geometricPrecision" text-rendering="geometricPrecision"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512"
	 style="enable-background:new 0 0 512 512;" xml:space="preserve">
<style type="text/css">
	.st0{fill-rule:evenodd;clip-rule:evenodd;fill:#FFBC08;}
	.st1{fill-rule:evenodd;clip-rule:evenodd;fill:#FFDC0B;}
	.st2{fill-rule:evenodd;clip-rule:evenodd;fill:#FF9E05;}
	.st3{fill:#FFBC08;}
</style>
<g id="Layer_x0020_1">
	<g id="_220148520">
		<path id="_220149360" class="st0" d="M323.2,378.2c-28.2-17.3-41.6-49-46.9-86.6h-40.5c-6.1,42.8-21.6,71-46.9,86.6l63.9,7.5
			L323.2,378.2z"/>
		<path id="_220149192" class="st1" d="M336.4,442.3v-64.1h-13.2H188.8h-13.2v64.1H336.4z"/>
		<path id="_220149168" class="st1" d="M321.6,257.7l20.4-26.3c18.6-29.3,30.5-66.1,35.7-110.5l2.9-65.7c-0.1-3.1-0.1-6.2-0.2-9.4
			c-0.3-7.7-6.3-13.8-13.6-13.8H145.3c-7.3,0-13.3,6.1-13.6,13.8c-0.1,3.2-0.2,6.3-0.2,9.4l2.9,65.7c5.2,44.4,17.1,81.3,35.7,110.5
			l20.4,26.3c14.6,15.4,31.8,27.7,51.8,37c9.1,4.2,18.4,4.2,27.5,0C289.7,285.5,307,273.1,321.6,257.7z"/>
		<path id="_220149216" class="st2" d="M143.1,438.6h225.7V480H143.1V438.6z"/>
		<g>
			<path id="_220149456" class="st3" d="M111.8,106.5c-3.9,6.6-2.2,15.4,3.8,19.6c6.1,4.3,14.2,2.4,18.1-4.2
				c7.3-12.2,9.8-26.3,8.1-39.7c-5.1-38.7-43.1-61.4-76.2-44.8c-24.7,12.4-36.1,40.4-33.1,69c4.1,39.8,39.9,92.1,127.3,147.9
				c9.2,4.8,5.4,19.5-6.3,16.4c-6.8-2.7-14.3,1.1-16.8,8.5c-3.3,9.8,2.8,17.6,11.7,19c41.2,6,60.7-45.3,21.1-67.1
				c-78.9-50.3-107.7-95.4-111-127.7c-1.7-16.3,3.6-32.8,18-40.1c17-8.5,36.7,3,39.3,22.9C116.8,93.1,115.5,100.3,111.8,106.5z"/>
			<path id="_220149624" class="st3" d="M400.2,106.5c3.9,6.6,2.2,15.4-3.8,19.6c-6.1,4.3-14.2,2.4-18.1-4.2
				c-7.3-12.2-9.8-26.3-8.1-39.7c5.1-38.7,43.1-61.4,76.2-44.8c24.7,12.4,36.1,40.4,33.1,69c-4.1,39.8-39.9,92.1-127.3,147.9
				c-9.2,4.8-5.4,19.5,6.3,16.4c6.8-2.7,14.3,1.1,16.8,8.5c3.3,9.8-2.8,17.6-11.7,19c-41.2,6-60.7-45.3-21.1-67.1
				c78.9-50.3,107.6-95.4,110.9-127.7c1.7-16.3-3.6-32.8-18-40.1c-17-8.5-36.7,3-39.3,22.9C395.2,93.1,396.5,100.3,400.2,106.5
				L400.2,106.5z"/>
		</g>
		<path id="_220148712" class="st2" d="M256,66.5l19.6,41.3l45.3,5.9L287.7,145l8.4,44.9L256,168l-40.1,21.9l8.4-44.9l-33.2-31.4
			l45.3-5.9L256,66.5z"/>
	</g>
</g>
<script>//<![CDATA[  <-- For SVG support
	if ('WebSocket' in window) {
		(function () {
			function refreshCSS() {
				var sheets = [].slice.call(document.getElementsByTagName("link"));
				var head = document.getElementsByTagName("head")[0];
				for (var i = 0; i < sheets.length; ++i) {
					var elem = sheets[i];
					var parent = elem.parentElement || head;
					parent.removeChild(elem);
					var rel = elem.rel;
					if (elem.href && typeof rel != "string" || rel.length == 0 || rel.toLowerCase() == "stylesheet") {
						var url = elem.href.replace(/(&|\?)_cacheOverride=\d+/, '');
						elem.href = url + (url.indexOf('?') >= 0 ? '&' : '?') + '_cacheOverride=' + (new Date().valueOf());
					}
					parent.appendChild(elem);
				}
			}
			var protocol = window.location.protocol === 'http:' ? 'ws://' : 'wss://';
			var address = protocol + window.location.host + window.location.pathname + '/ws';
			var socket = new WebSocket(address);
			socket.onmessage = function (msg) {
				if (msg.data == 'reload') window.location.reload();
				else if (msg.data == 'refreshcss') refreshCSS();
			};
			if (sessionStorage && !sessionStorage.getItem('IsThisFirstTime_Log_From_LiveServer')) {
				console.log('Live reload enabled.');
				sessionStorage.setItem('IsThisFirstTime_Log_From_LiveServer', true);
			}
		})();
	}
	else {
		console.error('Upgrade your browser. This Browser is NOT supported WebSocket for Live-Reloading.');
	}
	// 
	
	]]>
</script>
</svg>
