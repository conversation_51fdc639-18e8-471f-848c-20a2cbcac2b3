
$(window).scroll(function () {
    var sticky = $(".main-app-main-header"),
        scroll = $(window).scrollTop();

    if (scroll >= 100) sticky.addClass("fixed-header");
    else sticky.removeClass("fixed-header");
});



$(document).ready(function() {
    //nice scroll
    $(".scrollmenu").niceScroll({
        cursor:" #212335",
        cursorwidth: '2px', 
        autohidemode: true, 
        zindex: 999, 
        cursorborder:"none", 
        cursoropacitymin:"0", 
        cursoropacitymax:"1",
    });

    //toggle the component with class accordion_body
    $(".accordion_head").click(function() {
        if ($('.accordion_body').is(':visible')) {
          $(".accordion_body").slideUp(300);
          $(".plusminus").text('+');
        }
        if ($(this).next(".accordion_body").is(':visible')) {
          $(this).next(".accordion_body").slideUp(300);
          $(this).children(".plusminus").text('+');
        } else {
          $(this).next(".accordion_body").slideDown(300);
          $(this).children(".plusminus").text('-');
        }
      });


    //pop up
      $('#ad-popup-wrapper') .delay(4000) .fadeIn('1000') .delay(3000);

      $('#close-popup').click(function() {
        $('#ad-popup-wrapper').fadeOut(300);
      });

      $('#close-1').click(function() {
        $('.overlay').fadeOut(300);
      });

      $('#close-2').click(function() {
        $('.overlay').fadeOut(300);
      });

      $('#close-3').click(function() {
        $('.overlay').fadeOut(300);
      });

      $('#trigger-1').click(function() {
          $('#overlay-1').fadeIn(300);  
      });

      $('#trigger-2').click(function() {
        $('#overlay-2').fadeIn(300);  
      });

      $('#trigger-3').click(function() {
        $('#overlay-3').fadeIn(300);  
      });

      $("#myCollapsible").collapse({
        toggle: true,
    });

});

function openNav() {
  document.getElementById("mySidenav").style.width = "100%";
}

function closeNav() {
  document.getElementById("mySidenav").style.width = "0";
}


//toggle the component with moreOption Menu
function myFunction() {
  var x = document.getElementById("myLinks");
  if (x.style.display === "block") {
    x.style.display = "none";
  } else {
    x.style.display = "block";
  }
}


function myMenuIcon(x) {
  x.classList.toggle('change')
}

// ---------------Pic A Box Scripts------------------------------

// ---------------Pick A box Tabs
function openPickABox(evt, cityName) {
  var i, tabcontent, tablinks;
  tabcontent = document.getElementsByClassName("tabcontent");
  for (i = 0; i < tabcontent.length; i++) {
      tabcontent[i].style.display = "none";
  }
  tablinks = document.getElementsByClassName("tablinks");
  for (i = 0; i < tablinks.length; i++) {
      tablinks[i].className = tablinks[i].className.replace(" active", "");
  }
  document.getElementById(cityName).style.display = "block";
  evt.currentTarget.className += " active";
}


// ---------------Open A Race
function openRace(evt, cityName) {
  var i, tabcontent, tablinks;
  tabcontent = document.getElementsByClassName("tabcontent");
  for (i = 0; i < tabcontent.length; i++) {
      tabcontent[i].style.display = "none";
  }
  tablinks = document.getElementsByClassName("tablinks");
  for (i = 0; i < tablinks.length; i++) {
      tablinks[i].className = tablinks[i].className.replace(" active", "");
  }
  document.getElementById(cityName).style.display = "block";
  evt.currentTarget.className += " active";
}

// ---------------Pic A Box
$(document).ready(function () {
  $(".pick-a-box-game").click(function () {
      var xyz = $(this).addClass(".active-box");
      if (xyz.hasClass("active-box")) {
          xyz.removeClass("active-box");
      } else {
          $(".pick-a-box-game").removeClass("slow");
          $(".pick-a-box-game").removeClass("active-box");
          xyz.addClass("active-box");
      }
  });

 

  // ---------------Select Amount

  $(".choose-amount-to-deposit-wrap button.btn").click(function () {
      var xyz = $(this).addClass(".amount-selected");
      if (xyz.hasClass("amount-selected")) {
          xyz.removeClass("amount-selected");
      } else {
          $(".choose-amount-to-deposit-wrap button.btn").removeClass("slow");
          $(".choose-amount-to-deposit-wrap button.btn").removeClass("amount-selected");
          xyz.addClass("amount-selected");
      }
  });

  // ---------------Add and Remove auto Number

  $(".count").prop("disabled", true);
  $(document).on("click", ".plus", function () {
      $(".count").val(parseInt($(".count").val()) + 1);
  });

  $(document).on("click", ".minus", function () {
      $(".count").val(parseInt($(".count").val()) - 1);
      if ($(".count").val() == -1) {
          $(".count").val(0);
      }
  });
});

// ---------------Icon Rotate

$(".rotate").click(function () {
  $(this).toggleClass("down");
});

// ---------------Select Amount

$(document).ready(function () {
  $("#tab-content div").hide();
  $("#tab-content div:first").show();

  $("#nav li").click(function () {
      $("#nav li a").removeClass("active");
      $(this).find("a").addClass("active");
      $("#tab-content div").hide();

      var indexer = $(this).index(); //gets the current index of (this) which is #nav li
      $("#tab-content div:eq(" + indexer + ")").fadeIn(); //uses whatever index the link has to open the corresponding box
  });
});

// ---------------useFreebet
$(function () {
  $("#useFreebet").click(function () {
      if ($(this).is(":checked")) {
          $("#UseAvailableFreebet").show();
          $("#UseAmount").hide();
      } else {
          $("#UseAvailableFreebet").hide();
          $("#UseAmount").show();
      }
  });
});

$('.selectpicker').selectpicker();
